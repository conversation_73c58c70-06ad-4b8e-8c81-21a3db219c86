import numpy as np
import math
from config import *

class AIAgent:
    """AI Agent interface for reinforcement learning integration"""
    
    def __init__(self, player_id):
        self.player_id = player_id
        self.action_space_size = NUM_ACTIONS
        self.observation_space_size = OBS_SIZE
        
        # Action mapping
        self.action_map = {
            ACTION_MOVE_UP: "move_up",
            ACTION_MOVE_DOWN: "move_down", 
            ACTION_MOVE_LEFT: "move_left",
            ACTION_MOVE_RIGHT: "move_right",
            ACTION_AIM_UP: "aim_up",
            ACTION_AIM_DOWN: "aim_down",
            ACTION_AIM_LEFT: "aim_left", 
            ACTION_AIM_RIGHT: "aim_right",
            ACTION_SHOOT: "shoot",
            ACTION_RELOAD: "reload",
            ACTION_NO_OP: "no_op"
        }
        
        # State tracking
        self.last_action = ACTION_NO_OP
        self.last_observation = None
        self.last_reward = 0.0
        
    def get_observation(self, player, all_players, bullets):
        """Convert game state to observation vector for RL model"""
        obs = np.zeros(self.observation_space_size, dtype=np.float32)
        idx = 0
        
        # Player features (8 features)
        obs[idx:idx+8] = [
            player.x / SCREEN_WIDTH,           # Normalized x position
            player.y / SCREEN_HEIGHT,          # Normalized y position  
            player.hp / PLAYER_MAX_HP,         # Normalized health
            player.ammo / PLAYER_MAX_AMMO,     # Normalized ammo
            player.aim_x,                      # Aim direction x
            player.aim_y,                      # Aim direction y
            1.0 if player.team == 1 else 0.0, # Team (binary)
            1.0 if player.alive else 0.0      # Alive status
        ]
        idx += OBS_PLAYER_FEATURES
        
        # Enemy features (up to 9 enemies, 6 features each)
        enemies = [p for p in all_players if p.id != player.id and p.team != player.team]
        enemies.sort(key=lambda e: player.get_distance_to(e) if e.alive else float('inf'))
        
        for i in range(OBS_MAX_ENEMIES):
            if i < len(enemies) and enemies[i].alive:
                enemy = enemies[i]
                distance = player.get_distance_to(enemy)
                
                obs[idx:idx+6] = [
                    enemy.x / SCREEN_WIDTH,        # Normalized x position
                    enemy.y / SCREEN_HEIGHT,       # Normalized y position
                    enemy.hp / PLAYER_MAX_HP,      # Normalized health
                    1.0 if enemy.team == 1 else 0.0, # Team
                    1.0,                           # Alive (always true here)
                    min(distance / BULLET_RANGE, 1.0) # Normalized distance
                ]
            else:
                # Fill with zeros for missing enemies
                obs[idx:idx+6] = [0.0] * 6
            
            idx += OBS_ENEMY_FEATURES
        
        # Bullet features (up to 20 bullets, 4 features each)
        # Only include bullets that could affect this player
        relevant_bullets = []
        for bullet in bullets:
            if bullet.active and bullet.team != player.team:
                # Calculate if bullet is heading towards player
                to_player_x = player.x - bullet.x
                to_player_y = player.y - bullet.y
                bullet_dir_x = bullet.vel_x / BULLET_SPEED if bullet.vel_x != 0 else 0
                bullet_dir_y = bullet.vel_y / BULLET_SPEED if bullet.vel_y != 0 else 0
                
                # Dot product to see if bullet is heading towards player
                dot_product = (to_player_x * bullet_dir_x + to_player_y * bullet_dir_y)
                if dot_product > 0:  # Bullet heading towards player
                    distance = math.sqrt(to_player_x**2 + to_player_y**2)
                    relevant_bullets.append((bullet, distance))
        
        # Sort by distance
        relevant_bullets.sort(key=lambda x: x[1])
        
        for i in range(OBS_MAX_BULLETS):
            if i < len(relevant_bullets):
                bullet, _ = relevant_bullets[i]
                obs[idx:idx+4] = [
                    bullet.x / SCREEN_WIDTH,       # Normalized x position
                    bullet.y / SCREEN_HEIGHT,      # Normalized y position
                    bullet.vel_x / BULLET_SPEED,   # Normalized velocity x
                    bullet.vel_y / BULLET_SPEED    # Normalized velocity y
                ]
            else:
                # Fill with zeros for missing bullets
                obs[idx:idx+4] = [0.0] * 4
            
            idx += OBS_BULLET_FEATURES
        
        self.last_observation = obs
        return obs
    
    def action_to_game_commands(self, action, player, dt):
        """Convert RL action to game commands"""
        if action not in self.action_map:
            action = ACTION_NO_OP

        self.last_action = action
        command = self.action_map[action]

        # Execute the action
        if command == "move_up":
            player.move("up")
        elif command == "move_down":
            player.move("down")
        elif command == "move_left":
            player.move("left")
        elif command == "move_right":
            player.move("right")
        elif command == "aim_up":
            player.aim("up")
        elif command == "aim_down":
            player.aim("down")
        elif command == "aim_left":
            player.aim("left")
        elif command == "aim_right":
            player.aim("right")
        elif command == "shoot":
            # Need to pass players list for line-of-sight shooting
            return player.shoot([])  # Empty list for now, will be fixed in environment
        elif command == "reload":
            player.reload()
        # no_op does nothing

        return None
    
    def get_action_mask(self, player):
        """Get mask of valid actions (for action masking in RL)"""
        mask = np.ones(self.action_space_size, dtype=bool)
        
        if not player.alive:
            # Only no-op is valid when dead
            mask[:] = False
            mask[ACTION_NO_OP] = True
            return mask
        
        # Can't shoot if no ammo or reloading
        if player.ammo <= 0 or player.is_reloading:
            mask[ACTION_SHOOT] = False
        
        # Can't reload if already reloading or ammo is full
        if player.is_reloading or player.ammo == player.max_ammo:
            mask[ACTION_RELOAD] = False
        
        # Movement restrictions based on position
        if player.x <= PLAYER_SIZE:
            mask[ACTION_MOVE_LEFT] = False
        if player.x >= SCREEN_WIDTH - PLAYER_SIZE:
            mask[ACTION_MOVE_RIGHT] = False
        if player.y <= PLAYER_SIZE:
            mask[ACTION_MOVE_UP] = False
        if player.y >= SCREEN_HEIGHT - PLAYER_SIZE:
            mask[ACTION_MOVE_DOWN] = False
        
        return mask
    
    def get_action_probabilities(self, observation):
        """Get action probabilities from observation (placeholder for RL model)"""
        # This would be replaced by actual RL model inference
        # For now, return uniform probabilities for valid actions
        probs = np.ones(self.action_space_size) / self.action_space_size
        return probs
    
    def select_action(self, observation, epsilon=0.1):
        """Select action using epsilon-greedy strategy (placeholder)"""
        # This would be replaced by actual RL model action selection
        if np.random.random() < epsilon:
            # Random action
            return np.random.randint(0, self.action_space_size)
        else:
            # Greedy action (placeholder - would use model prediction)
            probs = self.get_action_probabilities(observation)
            return np.argmax(probs)
    
    def update_reward(self, reward):
        """Update the last received reward"""
        self.last_reward = reward
    
    def reset(self):
        """Reset agent state for new episode"""
        self.last_action = ACTION_NO_OP
        self.last_observation = None
        self.last_reward = 0.0
    
    def get_state_dict(self):
        """Get agent state for logging/debugging"""
        return {
            'player_id': self.player_id,
            'last_action': self.last_action,
            'last_reward': self.last_reward,
            'observation_shape': self.observation_space_size,
            'action_space_size': self.action_space_size
        }

class DummyAI(AIAgent):
    """Simple dummy AI for testing"""
    
    def __init__(self, player_id):
        super().__init__(player_id)
        self.target_enemy_id = None
        self.last_action_time = 0
        self.action_duration = 0.5  # Hold actions for 0.5 seconds
    
    def select_action(self, observation, player, all_players, current_time):
        """Simple rule-based action selection"""
        if not player.alive:
            return ACTION_NO_OP
        
        # Find nearest enemy
        nearest_enemy = None
        nearest_distance = float('inf')
        
        for other in all_players:
            if other.team != player.team and other.alive:
                distance = player.get_distance_to(other)
                if distance < nearest_distance:
                    nearest_distance = distance
                    nearest_enemy = other
        
        if not nearest_enemy:
            return ACTION_NO_OP
        
        # Calculate direction to enemy
        dx = nearest_enemy.x - player.x
        dy = nearest_enemy.y - player.y
        
        # Reload if low on ammo
        if player.ammo < 5 and not player.is_reloading:
            return ACTION_RELOAD
        
        # Shoot if enemy is close and we have ammo
        if nearest_distance < BULLET_RANGE * 0.8 and player.ammo > 0 and not player.is_reloading:
            # Aim at enemy first
            if abs(dx) > abs(dy):
                if dx > 0:
                    return ACTION_AIM_RIGHT
                else:
                    return ACTION_AIM_LEFT
            else:
                if dy > 0:
                    return ACTION_AIM_DOWN
                else:
                    return ACTION_AIM_UP
            
            # If already aimed correctly, shoot
            aim_dx = nearest_enemy.x - (player.x + math.cos(player.direction) * 50)
            aim_dy = nearest_enemy.y - (player.y + math.sin(player.direction) * 50)
            if abs(aim_dx) < 30 and abs(aim_dy) < 30:
                return ACTION_SHOOT
        
        # Move towards enemy
        if abs(dx) > abs(dy):
            if dx > 0:
                return ACTION_MOVE_RIGHT
            else:
                return ACTION_MOVE_LEFT
        else:
            if dy > 0:
                return ACTION_MOVE_DOWN
            else:
                return ACTION_MOVE_UP
