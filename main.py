import pygame
import random
import math
import time
import sys
from config import *
from player import Player, Bullet
from reward_manager import RewardManager

class PUBGSandbox:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("PUBG AI Sandbox - 5v5 TDM")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 16)
        
        # Game state
        self.players = []
        self.bullets = []
        self.game_time = 0.0
        self.game_duration = GAME_DURATION
        self.running = True
        self.paused = False
        
        # Managers
        self.reward_manager = RewardManager()
        
        # Statistics
        self.kill_feed = []
        self.team_scores = {1: 0, 2: 0}
        
        # Initialize players
        self._initialize_players()
        
        # AI control (for future integration)
        self.ai_controlled_player = 0  # Player ID 0 will be AI controlled
    
    def _initialize_players(self):
        """Initialize all players with spawn positions"""
        self.players = []
        
        # Team 1 players
        for i in range(PLAYERS_PER_TEAM):
            spawn_area = TEAM_1_SPAWN_AREAS[i % len(TEAM_1_SPAWN_AREAS)]
            x = spawn_area[0] + random.uniform(-30, 30)
            y = spawn_area[1] + random.uniform(-30, 30)
            
            # Keep within bounds
            x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, x))
            y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, y))
            
            is_ai = (i == 0)  # First player is AI controlled
            player = Player(i, 1, x, y, is_ai)
            self.players.append(player)
            self.reward_manager.initialize_player(i, player)
        
        # Team 2 players
        for i in range(PLAYERS_PER_TEAM):
            player_id = i + PLAYERS_PER_TEAM
            spawn_area = TEAM_2_SPAWN_AREAS[i % len(TEAM_2_SPAWN_AREAS)]
            x = spawn_area[0] + random.uniform(-30, 30)
            y = spawn_area[1] + random.uniform(-30, 30)
            
            # Keep within bounds
            x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, x))
            y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, y))
            
            player = Player(player_id, 2, x, y, False)
            self.players.append(player)
            self.reward_manager.initialize_player(player_id, player)
    
    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_r:
                    self.reset_game()
    
    def handle_input(self, dt):
        """Handle continuous input for player control"""
        if self.paused:
            return
        
        keys = pygame.key.get_pressed()
        
        # Control player 0 (AI player) with keyboard for testing
        player = self.players[0]
        
        # Movement
        player.move("stop", dt)
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            player.move("up", dt)
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            player.move("down", dt)
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            player.move("left", dt)
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            player.move("right", dt)
        
        # Aiming
        if keys[pygame.K_i]:
            player.aim("up")
        elif keys[pygame.K_k]:
            player.aim("down")
        elif keys[pygame.K_j]:
            player.aim("left")
        elif keys[pygame.K_l]:
            player.aim("right")
        
        # Shooting
        if keys[pygame.K_SPACE]:
            bullet = player.shoot()
            if bullet:
                self.bullets.append(bullet)
        
        # Reloading
        if keys[pygame.K_r]:
            player.reload()
    
    def update_ai_players(self, dt):
        """Update AI-controlled players (dummy AI for now)"""
        for player in self.players:
            if not player.is_ai or not player.alive:
                continue
            
            # Simple dummy AI behavior
            # Find nearest enemy
            nearest_enemy = None
            nearest_distance = float('inf')
            
            for other in self.players:
                if other.team != player.team and other.alive:
                    distance = player.get_distance_to(other)
                    if distance < nearest_distance:
                        nearest_distance = distance
                        nearest_enemy = other
            
            if nearest_enemy:
                # Move towards enemy
                dx = nearest_enemy.x - player.x
                dy = nearest_enemy.y - player.y
                
                # Simple movement
                if abs(dx) > abs(dy):
                    if dx > 0:
                        player.move("right", dt)
                    else:
                        player.move("left", dt)
                else:
                    if dy > 0:
                        player.move("down", dt)
                    else:
                        player.move("up", dt)
                
                # Aim at enemy
                player.aim_at_point(nearest_enemy.x, nearest_enemy.y)
                
                # Shoot if in range
                if nearest_distance < BULLET_RANGE * 0.8:
                    bullet = player.shoot()
                    if bullet:
                        self.bullets.append(bullet)
                
                # Reload if low on ammo
                if player.ammo < 5:
                    player.reload()
    
    def update_bullets(self, dt):
        """Update all bullets and handle collisions"""
        active_bullets = []
        
        for bullet in self.bullets:
            bullet.update(dt)
            
            if not bullet.active:
                continue
            
            # Check collision with players
            hit_player = None
            for player in self.players:
                if (player.alive and 
                    player.id != bullet.owner_id and 
                    player.team != bullet.team):
                    
                    distance = math.sqrt((bullet.x - player.x)**2 + (bullet.y - player.y)**2)
                    if distance < PLAYER_SIZE:
                        hit_player = player
                        break
            
            if hit_player:
                # Handle hit
                was_killed = hit_player.take_damage(BULLET_DAMAGE, bullet.owner_id)
                bullet.active = False
                
                # Reward system
                self.reward_manager.calculate_hit_reward(bullet.owner_id, BULLET_DAMAGE)
                
                if was_killed:
                    # Handle kill
                    self.reward_manager.calculate_kill_reward(bullet.owner_id, hit_player.id)
                    self.reward_manager.calculate_death_penalty(hit_player.id)
                    
                    # Update scores
                    killer = next((p for p in self.players if p.id == bullet.owner_id), None)
                    if killer:
                        killer.kills += 1
                        self.team_scores[killer.team] += 1
                    
                    # Add to kill feed
                    killer_name = f"Player {bullet.owner_id}"
                    victim_name = f"Player {hit_player.id}"
                    self.kill_feed.append(f"{killer_name} killed {victim_name}")
                    if len(self.kill_feed) > 5:
                        self.kill_feed.pop(0)
            else:
                # Miss penalty
                if not bullet.active:  # Bullet went out of range
                    self.reward_manager.calculate_miss_penalty(bullet.owner_id)
            
            if bullet.active:
                active_bullets.append(bullet)
        
        self.bullets = active_bullets
    
    def update(self, dt):
        """Update game state"""
        if self.paused:
            return
        
        self.game_time += dt
        
        # Update players
        for player in self.players:
            player.update(dt)
            
            # Calculate step rewards
            game_state = {'players': self.players, 'bullets': self.bullets}
            self.reward_manager.calculate_step_reward(player.id, player, game_state)
        
        # Update AI players
        self.update_ai_players(dt)
        
        # Update bullets
        self.update_bullets(dt)
        
        # Check game end condition
        if self.game_time >= self.game_duration:
            self.end_game()
    
    def draw(self):
        """Render the game"""
        self.screen.fill(BACKGROUND_COLOR)
        
        # Draw players
        for player in self.players:
            player.draw(self.screen)
        
        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)
        
        # Draw UI
        self.draw_ui()
        
        pygame.display.flip()
    
    def draw_ui(self):
        """Draw user interface elements"""
        # Game timer
        time_left = max(0, self.game_duration - self.game_time)
        time_text = f"Time: {int(time_left // 60):02d}:{int(time_left % 60):02d}"
        text_surface = self.font.render(time_text, True, (255, 255, 255))
        self.screen.blit(text_surface, (10, 10))
        
        # Team scores
        score_text = f"Team 1: {self.team_scores[1]}  Team 2: {self.team_scores[2]}"
        score_surface = self.font.render(score_text, True, (255, 255, 255))
        self.screen.blit(score_surface, (10, 40))
        
        # Kill feed
        for i, kill in enumerate(self.kill_feed):
            kill_surface = self.small_font.render(kill, True, (255, 255, 0))
            self.screen.blit(kill_surface, (SCREEN_WIDTH - 200, 10 + i * 20))
        
        # Player 0 stats (AI player)
        if self.players:
            player = self.players[0]
            stats_text = f"AI Player - HP: {player.hp} Ammo: {player.ammo} K/D: {player.kills}/{player.deaths}"
            stats_surface = self.small_font.render(stats_text, True, (0, 255, 255))
            self.screen.blit(stats_surface, (10, SCREEN_HEIGHT - 30))
        
        # Controls
        if self.paused:
            pause_text = "PAUSED - Press SPACE to resume"
            pause_surface = self.font.render(pause_text, True, (255, 255, 0))
            text_rect = pause_surface.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            self.screen.blit(pause_surface, text_rect)
    
    def reset_game(self):
        """Reset the game to initial state"""
        self.game_time = 0.0
        self.bullets = []
        self.kill_feed = []
        self.team_scores = {1: 0, 2: 0}
        self.reward_manager.reset()
        self._initialize_players()
    
    def end_game(self):
        """Handle game end"""
        print("Game Over!")
        print(f"Final Score - Team 1: {self.team_scores[1]}, Team 2: {self.team_scores[2]}")
        
        # Print AI player stats
        ai_player = self.players[0]
        summary = self.reward_manager.get_episode_summary(0)
        print(f"AI Player Summary: {summary}")
        
        self.reset_game()
    
    def run(self):
        """Main game loop"""
        while self.running:
            dt = self.clock.tick(FPS) / 1000.0  # Delta time in seconds
            
            self.handle_events()
            self.handle_input(dt)
            self.update(dt)
            self.draw()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = PUBGSandbox()
    game.run()
