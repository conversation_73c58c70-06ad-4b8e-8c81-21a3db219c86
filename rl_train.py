import gymnasium as gym
import numpy as np
import pygame
import time
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.callbacks import BaseCallback
import os

from config import *
from player import Player, Bullet
from reward_manager import Reward<PERSON>anager
from ai_agent import AIAgent, DummyAI

class PUBGEnvironment(gym.Env):
    """Gymnasium environment for PUBG sandbox training"""
    
    def __init__(self, render_mode=None, headless=True):
        super().__init__()
        
        # Environment settings
        self.headless = headless
        self.render_mode = render_mode
        
        # Initialize Pygame if not headless
        if not self.headless:
            pygame.init()
            self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
            pygame.display.set_caption("PUBG AI Training")
            self.clock = pygame.time.Clock()
            self.font = pygame.font.Font(None, 24)
        
        # Define action and observation spaces
        self.action_space = gym.spaces.Discrete(NUM_ACTIONS)
        self.observation_space = gym.spaces.Box(
            low=0.0, high=1.0, shape=(OBS_SIZE,), dtype=np.float32
        )
        
        # Game state
        self.players = []
        self.bullets = []
        self.game_time = 0.0
        self.max_episode_steps = int(GAME_DURATION * FPS)
        self.current_step = 0
        
        # Managers
        self.reward_manager = RewardManager()
        self.ai_agent = AIAgent(0)  # Player 0 is the learning agent
        
        # Dummy AIs for other players
        self.dummy_ais = {}
        
        # Episode tracking
        self.episode_count = 0
        self.total_reward = 0.0
        
        self.reset()
    
    def reset(self, seed=None, options=None):
        """Reset environment for new episode"""
        super().reset(seed=seed)
        
        # Reset game state
        self.game_time = 0.0
        self.current_step = 0
        self.bullets = []
        self.total_reward = 0.0
        
        # Reset managers
        self.reward_manager.reset()
        self.ai_agent.reset()
        
        # Initialize players
        self._initialize_players()
        
        # Get initial observation
        ai_player = self.players[0]
        observation = self.ai_agent.get_observation(ai_player, self.players, self.bullets)
        
        self.episode_count += 1
        
        info = {
            'episode': self.episode_count,
            'game_time': self.game_time,
            'ai_player_hp': ai_player.hp,
            'ai_player_ammo': ai_player.ammo
        }
        
        return observation, info
    
    def step(self, action):
        """Execute one step in the environment"""
        dt = 1.0 / FPS
        self.current_step += 1
        self.game_time += dt
        
        # Get AI player
        ai_player = self.players[0]
        
        # Execute AI action
        bullet = self.ai_agent.action_to_game_commands(action, ai_player, dt)
        if bullet:
            self.bullets.append(bullet)
        
        # Update dummy AIs
        self._update_dummy_ais(dt)
        
        # Update game state
        self._update_players(dt)
        self._update_bullets(dt)
        
        # Calculate reward
        game_state = {'players': self.players, 'bullets': self.bullets}
        step_reward = self.reward_manager.get_shaped_reward(0, ai_player, action, game_state)
        self.total_reward += step_reward
        
        # Get new observation
        observation = self.ai_agent.get_observation(ai_player, self.players, self.bullets)
        
        # Check if episode is done
        terminated = not ai_player.alive or self.game_time >= GAME_DURATION
        truncated = self.current_step >= self.max_episode_steps
        done = terminated or truncated
        
        # Info for logging
        info = {
            'episode': self.episode_count,
            'game_time': self.game_time,
            'ai_player_hp': ai_player.hp,
            'ai_player_ammo': ai_player.ammo,
            'ai_player_kills': ai_player.kills,
            'ai_player_deaths': ai_player.deaths,
            'total_reward': self.total_reward,
            'step_reward': step_reward,
            'terminated': terminated,
            'truncated': truncated
        }
        
        # Render if not headless
        if not self.headless and self.render_mode == 'human':
            self.render()
        
        return observation, step_reward, done, False, info
    
    def render(self):
        """Render the environment"""
        if self.headless:
            return
        
        self.screen.fill(BACKGROUND_COLOR)
        
        # Draw players
        for player in self.players:
            player.draw(self.screen)
        
        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)
        
        # Draw UI
        self._draw_training_ui()
        
        pygame.display.flip()
        self.clock.tick(FPS)
    
    def _initialize_players(self):
        """Initialize all players"""
        self.players = []
        self.dummy_ais = {}
        
        # Team 1 players (AI player is first)
        for i in range(PLAYERS_PER_TEAM):
            spawn_area = TEAM_1_SPAWN_AREAS[i % len(TEAM_1_SPAWN_AREAS)]
            x = spawn_area[0] + np.random.uniform(-30, 30)
            y = spawn_area[1] + np.random.uniform(-30, 30)
            
            x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, x))
            y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, y))
            
            is_ai = (i == 0)  # First player is the learning AI
            player = Player(i, 1, x, y, is_ai)
            self.players.append(player)
            self.reward_manager.initialize_player(i, player)
            
            if not is_ai:
                self.dummy_ais[i] = DummyAI(i)
        
        # Team 2 players (all dummy AIs)
        for i in range(PLAYERS_PER_TEAM):
            player_id = i + PLAYERS_PER_TEAM
            spawn_area = TEAM_2_SPAWN_AREAS[i % len(TEAM_2_SPAWN_AREAS)]
            x = spawn_area[0] + np.random.uniform(-30, 30)
            y = spawn_area[1] + np.random.uniform(-30, 30)
            
            x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, x))
            y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, y))
            
            player = Player(player_id, 2, x, y, False)
            self.players.append(player)
            self.reward_manager.initialize_player(player_id, player)
            self.dummy_ais[player_id] = DummyAI(player_id)
    
    def _update_dummy_ais(self, dt):
        """Update all dummy AI players"""
        current_time = time.time()
        
        for player_id, dummy_ai in self.dummy_ais.items():
            player = self.players[player_id]
            if not player.alive:
                continue
            
            # Get action from dummy AI
            action = dummy_ai.select_action(None, player, self.players, current_time)
            
            # Execute action
            bullet = dummy_ai.action_to_game_commands(action, player, dt)
            if bullet:
                self.bullets.append(bullet)
    
    def _update_players(self, dt):
        """Update all players"""
        for player in self.players:
            player.update(dt)
    
    def _update_bullets(self, dt):
        """Update bullets and handle collisions"""
        active_bullets = []
        
        for bullet in self.bullets:
            bullet.update(dt)
            
            if not bullet.active:
                continue
            
            # Check collision with players
            hit_player = None
            for player in self.players:
                if (player.alive and 
                    player.id != bullet.owner_id and 
                    player.team != bullet.team):
                    
                    distance = np.sqrt((bullet.x - player.x)**2 + (bullet.y - player.y)**2)
                    if distance < PLAYER_SIZE:
                        hit_player = player
                        break
            
            if hit_player:
                was_killed = hit_player.take_damage(BULLET_DAMAGE, bullet.owner_id)
                bullet.active = False
                
                # Update rewards
                self.reward_manager.calculate_hit_reward(bullet.owner_id, BULLET_DAMAGE)
                
                if was_killed:
                    self.reward_manager.calculate_kill_reward(bullet.owner_id, hit_player.id)
                    self.reward_manager.calculate_death_penalty(hit_player.id)
                    
                    # Update kill count
                    killer = next((p for p in self.players if p.id == bullet.owner_id), None)
                    if killer:
                        killer.kills += 1
            
            if bullet.active:
                active_bullets.append(bullet)
        
        self.bullets = active_bullets
    
    def _draw_training_ui(self):
        """Draw training-specific UI"""
        ai_player = self.players[0]
        
        # Episode info
        episode_text = f"Episode: {self.episode_count} | Step: {self.current_step}"
        text_surface = self.font.render(episode_text, True, (255, 255, 255))
        self.screen.blit(text_surface, (10, 10))
        
        # AI player stats
        stats_text = f"AI: HP={ai_player.hp} Ammo={ai_player.ammo} K/D={ai_player.kills}/{ai_player.deaths}"
        stats_surface = self.font.render(stats_text, True, (0, 255, 255))
        self.screen.blit(stats_surface, (10, 40))
        
        # Reward info
        reward_text = f"Total Reward: {self.total_reward:.2f}"
        reward_surface = self.font.render(reward_text, True, (255, 255, 0))
        self.screen.blit(reward_surface, (10, 70))
    
    def close(self):
        """Clean up environment"""
        if not self.headless:
            pygame.quit()

class TrainingCallback(BaseCallback):
    """Custom callback for training monitoring"""
    
    def __init__(self, verbose=0):
        super().__init__(verbose)
        self.episode_rewards = []
        self.episode_lengths = []
    
    def _on_step(self) -> bool:
        # Log episode statistics
        if 'episode' in self.locals.get('infos', [{}])[0]:
            info = self.locals['infos'][0]
            if info.get('terminated', False) or info.get('truncated', False):
                self.episode_rewards.append(info.get('total_reward', 0))
                self.episode_lengths.append(self.num_timesteps)
                
                if len(self.episode_rewards) % 10 == 0:
                    avg_reward = np.mean(self.episode_rewards[-10:])
                    print(f"Episode {len(self.episode_rewards)}: Avg Reward (last 10): {avg_reward:.2f}")
        
        return True

def train_agent(total_timesteps=100000, save_path="pubg_ai_model"):
    """Train the PPO agent"""
    print("Starting PUBG AI Training...")
    
    # Create environment
    env = PUBGEnvironment(headless=True)
    
    # Create PPO model
    model = PPO(
        "MlpPolicy",
        env,
        verbose=1,
        learning_rate=3e-4,
        n_steps=2048,
        batch_size=64,
        n_epochs=10,
        gamma=0.99,
        gae_lambda=0.95,
        clip_range=0.2,
        ent_coef=0.01,
        tensorboard_log="./pubg_tensorboard/"
    )
    
    # Create callback
    callback = TrainingCallback()
    
    # Train the model
    model.learn(
        total_timesteps=total_timesteps,
        callback=callback,
        progress_bar=True
    )
    
    # Save the model
    model.save(save_path)
    print(f"Model saved to {save_path}")
    
    env.close()
    return model

def test_agent(model_path="pubg_ai_model", episodes=10):
    """Test the trained agent"""
    print("Testing trained agent...")
    
    # Create environment with rendering
    env = PUBGEnvironment(headless=False, render_mode='human')
    
    # Load model
    model = PPO.load(model_path)
    
    for episode in range(episodes):
        obs, info = env.reset()
        total_reward = 0
        done = False
        
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            done = terminated or truncated
            
            env.render()
        
        print(f"Episode {episode + 1}: Total Reward = {total_reward:.2f}")
    
    env.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="PUBG AI Training")
    parser.add_argument("--mode", choices=["train", "test"], default="train", help="Mode: train or test")
    parser.add_argument("--timesteps", type=int, default=100000, help="Training timesteps")
    parser.add_argument("--model", type=str, default="pubg_ai_model", help="Model path")
    parser.add_argument("--episodes", type=int, default=10, help="Test episodes")
    
    args = parser.parse_args()
    
    if args.mode == "train":
        train_agent(total_timesteps=args.timesteps, save_path=args.model)
    else:
        test_agent(model_path=args.model, episodes=args.episodes)
