import gymnasium as gym
import numpy as np
import pygame
import time
import math
import random
from stable_baselines3 import PPO
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.vec_env import DummyVecEnv, SubprocVecEnv
from stable_baselines3.common.callbacks import BaseCallback
import os

from config import *
from player import Player, Bullet
from reward_manager import <PERSON>wardManager
from ai_agent import AIAgent, DummyAI

class TDMEnv(gym.Env):
    """Custom Gym environment that wraps the PUBG TDM sandbox built with Pygame"""

    def __init__(self, render_mode=None, headless=True):
        super().__init__()

        # Environment settings
        self.headless = headless
        self.render_mode = render_mode

        # Initialize Pygame if not headless
        if not self.headless:
            pygame.init()
            self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
            pygame.display.set_caption("TDM AI Training")
            self.clock = pygame.time.Clock()
            self.font = pygame.font.Font(None, 24)

        # Define action and observation spaces
        self.action_space = gym.spaces.Discrete(NUM_ACTIONS)

        # Observation space: flattened state
        # [own_x, own_y, hp, ammo, enemy1_x, enemy1_y, enemy1_hp, ..., team_score, time_remaining]
        obs_size = 4 + (PLAYERS_PER_TEAM * 2 - 1) * 3 + 3  # self(4) + enemies(3 each) + game_state(3)
        self.observation_space = gym.spaces.Box(
            low=0.0, high=1.0, shape=(obs_size,), dtype=np.float32
        )

        # Game state
        self.players = []
        self.bullets = []
        self.game_time = 0.0
        self.max_episode_steps = int(GAME_DURATION * FPS)
        self.current_step = 0

        # AI components
        self.ai_player = None
        self.ai_agent = None
        self.reward_manager = None

        # Dummy AIs for other players
        self.dummy_ais = {}

        # Episode tracking
        self.episode_count = 0
        self.total_reward = 0.0

        # Team scores
        self.team_scores = {1: 0, 2: 0}
    
    def reset(self, seed=None, options=None):
        """Reset the game state, reset map and AI agent, return initial observation"""
        super().reset(seed=seed)

        # Reset game state
        self.game_time = 0.0
        self.current_step = 0
        self.bullets = []
        self.total_reward = 0.0
        self.team_scores = {1: 0, 2: 0}

        # Initialize players
        self._initialize_players()

        # Create AI components
        self.ai_player = self.players[0]  # First player is AI controlled
        self.ai_agent = AIAgent(0)
        self.reward_manager = RewardManager(self.ai_player)

        # Reset dummy AIs
        self.dummy_ais = {}
        for i, player in enumerate(self.players):
            if i != 0:  # Not the AI player
                self.dummy_ais[player.player_id] = DummyAI(player.player_id)

        # Get initial observation
        observation = self._get_observation()

        self.episode_count += 1

        info = {
            'episode': self.episode_count,
            'game_time': self.game_time,
            'ai_player_hp': self.ai_player.hp,
            'ai_player_ammo': self.ai_player.ammo,
            'team_scores': self.team_scores.copy()
        }

        return observation, info
    
    def step(self, action):
        """Apply action, simulate one frame, get reward, return (observation, reward, done, info)"""
        dt = 1.0 / FPS
        self.current_step += 1
        self.game_time += dt

        # Apply action using AIAgent
        bullet = self._apply_action(action, dt)
        if bullet:
            self.bullets.append(bullet)

        # Update dummy AIs
        self._update_dummy_ais(dt)

        # Simulate one frame of the game
        self._update_players(dt)
        self._update_bullets(dt)

        # Update reward manager
        current_time = time.time()
        self.reward_manager.update(current_time, self.players)

        # Get reward from RewardManager
        step_reward = self.reward_manager.get_reward()
        self.total_reward += step_reward

        # Get new observation (flattened state)
        observation = self._get_observation()

        # Check if episode is done
        # Done if: Player dies OR Match timer expires
        terminated = not self.ai_player.is_alive or self.game_time >= GAME_DURATION
        truncated = self.current_step >= self.max_episode_steps
        done = terminated or truncated

        # Info for logging
        info = {
            'episode': self.episode_count,
            'game_time': self.game_time,
            'ai_player_hp': self.ai_player.hp,
            'ai_player_ammo': self.ai_player.ammo,
            'ai_player_kills': self.ai_player.kills,
            'ai_player_deaths': self.ai_player.deaths,
            'total_reward': self.total_reward,
            'step_reward': step_reward,
            'terminated': terminated,
            'truncated': truncated,
            'team_scores': self.team_scores.copy()
        }

        # Render if not headless
        if not self.headless and self.render_mode == 'human':
            self.render()

        return observation, step_reward, done, False, info
    
    def render(self):
        """Optional rendering"""
        if self.headless:
            return

        self.screen.fill(BACKGROUND_COLOR)

        # Draw players
        for player in self.players:
            player.draw(self.screen)

        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # Draw UI
        self._draw_training_ui()

        pygame.display.flip()
        self.clock.tick(FPS)

    def _get_observation(self):
        """Return flattened state as NumPy array"""
        obs = []

        # Own player state: [x, y, hp, ammo] (normalized 0-1)
        obs.extend([
            self.ai_player.x / SCREEN_WIDTH,
            self.ai_player.y / SCREEN_HEIGHT,
            self.ai_player.hp / 100.0,
            self.ai_player.ammo / 30.0
        ])

        # Enemy states: [x, y, hp] for each enemy (normalized 0-1)
        enemies = [p for p in self.players if p.team != self.ai_player.team and p.is_alive]
        enemies.sort(key=lambda e: self.ai_player.get_distance_to(e))  # Sort by distance

        max_enemies = PLAYERS_PER_TEAM * 2 - 1  # Max possible enemies
        for i in range(max_enemies):
            if i < len(enemies):
                enemy = enemies[i]
                obs.extend([
                    enemy.x / SCREEN_WIDTH,
                    enemy.y / SCREEN_HEIGHT,
                    enemy.hp / 100.0
                ])
            else:
                # Pad with zeros for missing enemies
                obs.extend([0.0, 0.0, 0.0])

        # Game state: [team_score_diff, time_remaining, alive_teammates]
        # Convert team names to numbers for score lookup
        ai_team_num = 1 if self.ai_player.team == 'blue' else 2
        enemy_team_num = 2 if ai_team_num == 1 else 1

        score_diff = (self.team_scores[ai_team_num] - self.team_scores[enemy_team_num]) / 10.0  # Normalize
        time_remaining = max(0, GAME_DURATION - self.game_time) / GAME_DURATION

        alive_teammates = sum(1 for p in self.players
                             if p.team == self.ai_player.team and p.is_alive and p != self.ai_player)
        alive_teammates_norm = alive_teammates / (PLAYERS_PER_TEAM - 1)

        obs.extend([
            max(-1.0, min(1.0, score_diff)),  # Clamp score diff
            time_remaining,
            alive_teammates_norm
        ])

        return np.array(obs, dtype=np.float32)

    def _apply_action(self, action, dt):
        """Apply action using AIAgent and return any bullet created"""
        # Handle shooting action specially to pass players list
        if action == ACTION_SHOOT:
            return self.ai_player.shoot(self.players)
        else:
            return self.ai_agent.action_to_game_commands(action, self.ai_player, dt)
    
    def _initialize_players(self):
        """Initialize all players"""
        self.players = []

        # Team 1 players (AI player is first)
        for i in range(PLAYERS_PER_TEAM):
            spawn_area = TEAM_1_SPAWN_AREAS[i % len(TEAM_1_SPAWN_AREAS)]
            x = spawn_area[0] + random.uniform(-30, 30)
            y = spawn_area[1] + random.uniform(-30, 30)

            x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, x))
            y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, y))

            is_ai = (i == 0)  # First player is the learning AI
            player = Player(i, 1, x, y, is_ai)
            self.players.append(player)

        # Team 2 players (all dummy AIs)
        for i in range(PLAYERS_PER_TEAM):
            player_id = i + PLAYERS_PER_TEAM
            spawn_area = TEAM_2_SPAWN_AREAS[i % len(TEAM_2_SPAWN_AREAS)]
            x = spawn_area[0] + random.uniform(-30, 30)
            y = spawn_area[1] + random.uniform(-30, 30)

            x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, x))
            y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, y))

            player = Player(player_id, 2, x, y, False)
            self.players.append(player)
    
    def _update_dummy_ais(self, dt):
        """Update all dummy AI players"""
        current_time = time.time()

        for player_id, dummy_ai in self.dummy_ais.items():
            player = next((p for p in self.players if p.player_id == player_id), None)
            if not player or not player.is_alive:
                continue

            # Get action from dummy AI
            action = dummy_ai.select_action(None, player, self.players, current_time)

            # Execute action
            if action == ACTION_SHOOT:
                bullet = player.shoot(self.players)
                if bullet:
                    self.bullets.append(bullet)
            else:
                dummy_ai.action_to_game_commands(action, player, dt)
    
    def _update_players(self, dt):
        """Update all players"""
        for player in self.players:
            player.update(dt, self.players)

    def _update_bullets(self, dt):
        """Update bullets and handle collisions"""
        active_bullets = []

        for bullet in self.bullets:
            bullet.update(dt)

            if not bullet.active:
                continue

            # Check collision with players
            hit_player = None
            for player in self.players:
                if (player.is_alive and
                    player.player_id != bullet.owner_id and
                    player.team != bullet.team):

                    distance = math.sqrt((bullet.x - player.x)**2 + (bullet.y - player.y)**2)
                    if distance < PLAYER_RADIUS:
                        hit_player = player
                        break

            if hit_player:
                was_killed = hit_player.take_damage(BULLET_DAMAGE, bullet.owner_id)
                bullet.active = False

                if was_killed:
                    # Update team scores
                    killer = next((p for p in self.players if p.player_id == bullet.owner_id), None)
                    if killer:
                        killer.kills += 1
                        # Convert team name to number for score tracking
                        killer_team_num = 1 if killer.team == 'blue' else 2
                        self.team_scores[killer_team_num] += 1

            if bullet.active:
                active_bullets.append(bullet)

        self.bullets = active_bullets
    
    def _draw_training_ui(self):
        """Draw training-specific UI"""
        ai_player = self.players[0]
        
        # Episode info
        episode_text = f"Episode: {self.episode_count} | Step: {self.current_step}"
        text_surface = self.font.render(episode_text, True, (255, 255, 255))
        self.screen.blit(text_surface, (10, 10))
        
        # AI player stats
        stats_text = f"AI: HP={ai_player.hp} Ammo={ai_player.ammo} K/D={ai_player.kills}/{ai_player.deaths}"
        stats_surface = self.font.render(stats_text, True, (0, 255, 255))
        self.screen.blit(stats_surface, (10, 40))
        
        # Reward info
        reward_text = f"Total Reward: {self.total_reward:.2f}"
        reward_surface = self.font.render(reward_text, True, (255, 255, 0))
        self.screen.blit(reward_surface, (10, 70))
    
    def close(self):
        """Clean up environment"""
        if not self.headless:
            pygame.quit()

class TrainingCallback(BaseCallback):
    """Custom callback for training monitoring"""
    
    def __init__(self, verbose=0):
        super().__init__(verbose)
        self.episode_rewards = []
        self.episode_lengths = []
    
    def _on_step(self) -> bool:
        # Log episode statistics
        if 'episode' in self.locals.get('infos', [{}])[0]:
            info = self.locals['infos'][0]
            if info.get('terminated', False) or info.get('truncated', False):
                self.episode_rewards.append(info.get('total_reward', 0))
                self.episode_lengths.append(self.num_timesteps)
                
                if len(self.episode_rewards) % 10 == 0:
                    avg_reward = np.mean(self.episode_rewards[-10:])
                    print(f"Episode {len(self.episode_rewards)}: Avg Reward (last 10): {avg_reward:.2f}")
        
        return True

def train_agent(total_timesteps=100000, save_path="tdm_pro_ai"):
    """Train the PPO agent using Stable-Baselines3"""
    print("Starting TDM AI Training...")

    # Create environment
    env = TDMEnv(headless=True)

    # Create PPO model
    model = PPO(
        "MlpPolicy",
        env,
        verbose=1,
        learning_rate=3e-4,
        n_steps=2048,
        batch_size=64,
        n_epochs=10,
        gamma=0.99,
        gae_lambda=0.95,
        clip_range=0.2,
        ent_coef=0.01,
        tensorboard_log="./tdm_tensorboard/"
    )

    # Create callback
    callback = TrainingCallback()

    # Train the model
    print(f"Training for {total_timesteps} timesteps...")
    model.learn(
        total_timesteps=total_timesteps,
        callback=callback,
        progress_bar=True
    )

    # Save the model
    model.save(save_path)
    print(f"Model saved to {save_path}")

    env.close()
    return model

def test_agent(model_path="tdm_pro_ai", episodes=10):
    """Test the trained agent"""
    print("Testing trained agent...")

    # Create environment with rendering
    env = TDMEnv(headless=False, render_mode='human')

    # Load model
    model = PPO.load(model_path)

    for episode in range(episodes):
        obs, info = env.reset()
        total_reward = 0
        done = False
        step_count = 0

        print(f"Episode {episode + 1} starting...")

        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            done = terminated or truncated
            step_count += 1

            env.render()

            # Print progress every 100 steps
            if step_count % 100 == 0:
                print(f"  Step {step_count}: Reward = {total_reward:.2f}, "
                      f"HP = {info['ai_player_hp']}, K/D = {info['ai_player_kills']}/{info['ai_player_deaths']}")

        print(f"Episode {episode + 1} finished: Total Reward = {total_reward:.2f}, "
              f"Steps = {step_count}, K/D = {info['ai_player_kills']}/{info['ai_player_deaths']}")

    env.close()

def quick_test():
    """Quick test of the environment without training"""
    print("Quick testing TDM environment...")

    env = TDMEnv(headless=False, render_mode='human')

    for episode in range(3):
        obs, info = env.reset()
        total_reward = 0
        done = False
        step_count = 0

        print(f"Episode {episode + 1} - Random actions test")

        while not done and step_count < 1000:  # Limit steps for quick test
            # Random action
            action = env.action_space.sample()
            obs, reward, terminated, truncated, info = env.step(action)
            total_reward += reward
            done = terminated or truncated
            step_count += 1

            env.render()

            if step_count % 100 == 0:
                print(f"  Step {step_count}: Reward = {total_reward:.2f}")

        print(f"Episode {episode + 1} finished: Total Reward = {total_reward:.2f}, Steps = {step_count}")

    env.close()

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="TDM AI Training")
    parser.add_argument("--mode", choices=["train", "test", "quick"], default="train", help="Mode: train, test, or quick")
    parser.add_argument("--timesteps", type=int, default=100000, help="Training timesteps")
    parser.add_argument("--model", type=str, default="tdm_pro_ai", help="Model path")
    parser.add_argument("--episodes", type=int, default=10, help="Test episodes")

    args = parser.parse_args()

    if args.mode == "train":
        train_agent(total_timesteps=args.timesteps, save_path=args.model)
    elif args.mode == "test":
        test_agent(model_path=args.model, episodes=args.episodes)
    elif args.mode == "quick":
        quick_test()
