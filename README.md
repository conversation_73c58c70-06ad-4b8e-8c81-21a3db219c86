# 🎮 PUBG AI Sandbox - Complete RL Training Platform

A comprehensive 2D top-down arena shooter built with Pygame for training reinforcement learning AI agents. Features complete 5v5 team deathmatch gameplay with full state access, PPO training, and live match viewing.

## 🌟 Features

- **🎯 5v5 Arena Combat**: 10 players (5 per team) with respawn mechanics and line-of-sight shooting
- **🤖 Full AI Integration**: Complete observation space (34 features) and action space (11 actions) for RL training
- **⚔️ Realistic Combat**: Direction-based aiming, instant hit detection, reload mechanics, health system
- **🏆 Advanced Reward System**: Multi-layered rewards for kills (+1.0), deaths (-1.0), survival (+0.1/sec), multi-kills (+0.5)
- **🧠 PPO Training**: Ready-to-use Stable-Baselines3 integration with custom Gym environment
- **📊 Live Match Viewer**: Watch your trained AI compete in real-time 5v5 matches
- **📈 Performance Tracking**: Match logs, statistics, and performance analysis
- **🎨 Visual Debugging**: Real-time rendering with health bars, aim indicators, crosshairs, kill feed

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Test the Game (Manual Control)
```bash
python main.py
```

**Controls:**
- **Movement**: WASD or Arrow Keys
- **Aiming**: IJKL keys
- **Shoot**: Spacebar
- **Reload**: R key
- **Pause**: Spacebar (in menu)

### 3. Watch AI in Action (Live Match Viewer)
```bash
python watch_ai.py
```

### 4. Train AI Agent
```bash
# Quick demo training (5 minutes)
python quick_train_demo.py train

# Full training (longer)
python rl_train.py --mode train --timesteps 100000
```

### 5. Test Trained Agent
```bash
python rl_train.py --mode test --model tdm_pro_ai --episodes 10
```

## Game Mechanics

### Map & Players
- **Map Size**: 800x600 pixels
- **Teams**: Blue (Team 1) vs Red (Team 2)
- **Player Stats**: 100 HP, 30 ammo capacity
- **Respawn Time**: 3 seconds

### Combat System
- **Bullet Speed**: 400 px/s
- **Bullet Range**: 200 pixels
- **Bullet Damage**: 25 HP
- **Reload Time**: 2 seconds
- **Shoot Cooldown**: 0.1 seconds

### AI Observation Space
- **Player Features**: Position, health, ammo, aim direction, team, alive status
- **Enemy Features**: Up to 9 enemies with position, health, distance
- **Bullet Features**: Up to 20 bullets with position and velocity
- **Total Observation Size**: 134 features

### Action Space
- **Movement**: Up, Down, Left, Right
- **Aiming**: Up, Down, Left, Right
- **Combat**: Shoot, Reload
- **Utility**: No-op

### Reward System
- **Kill**: +1.0
- **Death**: -1.0
- **Hit Enemy**: +0.2
- **Miss Shot**: -0.05
- **Survival Tick**: +0.1

## 🎬 Live Match Viewer

Watch your trained AI compete in real-time 5v5 matches!

### Features
- **Live 5v5 Matches**: AI + 4 bots vs 5 bots
- **Real-time Stats**: Kill/death ratios, team scores, match duration
- **Visual Effects**: AI crosshair, bullet trails, kill feed
- **Match Logs**: Automatic saving to `match_logs/` folder
- **Interactive Controls**: Pause, restart, toggle effects

### Usage
```bash
# Auto-detect and launch best available model
python watch_ai.py

# Launch specific model
python tdm_match_viewer.py tdm_pro_ai
```

### Controls
- **SPACE**: Pause/Resume match
- **R**: Restart match
- **C**: Toggle AI crosshair
- **ESC**: Exit viewer

## File Structure

```
pubg_ai_sandbox/
├── main.py                 # Main game loop (manual testing)
├── config.py               # Game constants and settings
├── player.py               # Player and Bullet classes with line-of-sight shooting
├── ai_agent.py             # AI agent interface and dummy AI
├── reward_manager.py       # Advanced reward calculation system
├── rl_train.py             # PPO training with custom Gym environment
├── tdm_match_viewer.py     # Live match viewer for trained AI
├── watch_ai.py             # Simple launcher for match viewer
├── quick_train_demo.py     # Quick training demonstration
├── test_match_viewer.py    # Test and setup script
├── requirements.txt        # Python dependencies
├── match_logs/             # Saved match statistics (auto-created)
└── README.md              # This file
```

## Training Configuration

The PPO agent is configured with:
- **Learning Rate**: 3e-4
- **Batch Size**: 64
- **Steps per Update**: 2048
- **Training Epochs**: 10
- **Discount Factor**: 0.99
- **GAE Lambda**: 0.95

## Customization

### Modify Game Settings
Edit `config.py` to change:
- Map size and spawn positions
- Player stats (HP, ammo, speed)
- Weapon parameters (damage, range, reload time)
- Reward values
- AI observation/action space

### Add New Features
- **Weapons**: Extend bullet system in `player.py`
- **Power-ups**: Add items in main game loop
- **Maps**: Modify spawn areas and add obstacles
- **Game Modes**: Implement different victory conditions

### Advanced AI Training
- **Multi-agent**: Train multiple AI agents simultaneously
- **Curriculum Learning**: Gradually increase difficulty
- **Self-play**: Train agents against previous versions
- **Custom Rewards**: Modify reward functions for specific behaviors

## Performance

- **Training Speed**: ~1000 steps/second (headless mode)
- **Memory Usage**: ~200MB during training
- **Episode Length**: 5 minutes (18,000 steps at 60 FPS)

## Next Steps

1. **Test the basic game** with manual controls
2. **Run a short training session** (10k timesteps) to verify setup
3. **Experiment with reward functions** to shape desired behaviors
4. **Scale up training** with longer episodes and more timesteps
5. **Implement advanced features** like team coordination rewards

## Troubleshooting

### Common Issues
- **Pygame not displaying**: Check if running in headless environment
- **Training too slow**: Reduce FPS or use headless mode
- **Agent not learning**: Adjust reward scaling or learning rate
- **Memory issues**: Reduce observation space or batch size

### Debug Mode
Set `DEBUG_MODE = True` in `config.py` for additional logging and visual indicators.
