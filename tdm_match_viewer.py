#!/usr/bin/env python3
"""
TDM Match Viewer - Watch your trained AI in live 5v5 matches
Loads a trained PPO model and runs real-time PUBG-style TDM matches in Pygame
"""

import pygame
import numpy as np
import time
import json
import os
from datetime import datetime
from stable_baselines3 import PPO

from config import *
from player import Player, Bullet
from reward_manager import Re<PERSON><PERSON>anager
from ai_agent import Dummy<PERSON><PERSON>
from rl_train import TDMEnv

class TDMMatchViewer:
    """Live match viewer for trained AI agents"""
    
    def __init__(self, model_path="tdm_pro_ai"):
        # Initialize Pygame
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("TDM Match Viewer - AI vs Bots")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 16)
        
        # Load trained model
        try:
            self.model = PPO.load(model_path)
            print(f"✅ Loaded trained model: {model_path}")
        except FileNotFoundError:
            print(f"❌ Model not found: {model_path}")
            print("Please train a model first using: python rl_train.py --mode train")
            raise
        
        # Create environment for observation generation
        self.env = TDMEnv(headless=True)
        
        # Game state
        self.players = []
        self.bullets = []
        self.game_time = 0.0
        self.match_duration = GAME_DURATION
        self.running = True
        self.paused = False
        
        # AI components
        self.ai_player = None
        self.reward_manager = None
        self.dummy_ais = {}
        
        # Match statistics
        self.team_scores = {1: 0, 2: 0}
        self.kill_feed = []
        self.match_start_time = time.time()
        self.match_stats = {
            'ai_kills': 0,
            'ai_deaths': 0,
            'ai_damage_dealt': 0,
            'total_kills': 0,
            'match_duration': 0,
            'winner': None
        }
        
        # Visual effects
        self.crosshair_visible = True
        self.bullet_trails = []
        
        # Initialize match
        self._initialize_match()
    
    def _initialize_match(self):
        """Initialize a new match"""
        print("🎮 Initializing 5v5 TDM Match...")
        
        # Reset game state
        self.game_time = 0.0
        self.bullets = []
        self.bullet_trails = []
        self.kill_feed = []
        self.team_scores = {1: 0, 2: 0}
        self.match_start_time = time.time()
        
        # Create players
        self.players = []
        self.dummy_ais = {}
        
        # Team 1 players (AI player is first)
        for i in range(PLAYERS_PER_TEAM):
            spawn_area = TEAM_1_SPAWN_AREAS[i % len(TEAM_1_SPAWN_AREAS)]
            x = spawn_area[0] + np.random.uniform(-30, 30)
            y = spawn_area[1] + np.random.uniform(-30, 30)
            
            x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, x))
            y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, y))
            
            is_ai = (i == 0)  # First player is AI controlled
            player = Player(i, 1, x, y, is_ai)
            self.players.append(player)
            
            if is_ai:
                self.ai_player = player
                self.reward_manager = RewardManager(player)
                print(f"🤖 AI Player: {player.player_id} (Team Blue)")
            else:
                self.dummy_ais[player.player_id] = DummyAI(player.player_id)
        
        # Team 2 players (all dummy bots)
        for i in range(PLAYERS_PER_TEAM):
            player_id = i + PLAYERS_PER_TEAM
            spawn_area = TEAM_2_SPAWN_AREAS[i % len(TEAM_2_SPAWN_AREAS)]
            x = spawn_area[0] + np.random.uniform(-30, 30)
            y = spawn_area[1] + np.random.uniform(-30, 30)
            
            x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, x))
            y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, y))
            
            player = Player(player_id, 2, x, y, False)
            self.players.append(player)
            self.dummy_ais[player_id] = DummyAI(player_id)
        
        print(f"👥 Created {len(self.players)} players ({PLAYERS_PER_TEAM}v{PLAYERS_PER_TEAM})")
        print("🔵 Team 1 (Blue): AI + 4 Bots")
        print("🔴 Team 2 (Red): 5 Bots")
    
    def _get_ai_observation(self):
        """Get observation for AI player using the environment"""
        # Temporarily set environment state
        self.env.players = self.players
        self.env.bullets = self.bullets
        self.env.ai_player = self.ai_player
        self.env.game_time = self.game_time
        self.env.team_scores = self.team_scores
        
        return self.env._get_observation()
    
    def _update_ai_player(self, dt):
        """Update AI player using trained model"""
        if not self.ai_player.is_alive:
            return
        
        # Get observation
        obs = self._get_ai_observation()
        
        # Get action from trained model
        action, _ = self.model.predict(obs, deterministic=True)
        
        # Apply action
        if action == ACTION_SHOOT:
            bullet = self.ai_player.shoot(self.players)
            if bullet:
                self.bullets.append(bullet)
                # Add bullet trail effect
                self.bullet_trails.append({
                    'start': (self.ai_player.x, self.ai_player.y),
                    'end': (bullet.x, bullet.y),
                    'time': time.time(),
                    'duration': 0.1
                })
        else:
            # Use environment's action application
            self.env.ai_player = self.ai_player
            self.env._apply_action(action, dt)
    
    def _update_dummy_ais(self, dt):
        """Update all dummy AI players"""
        current_time = time.time()
        
        for player_id, dummy_ai in self.dummy_ais.items():
            player = next((p for p in self.players if p.player_id == player_id), None)
            if not player or not player.is_alive:
                continue
            
            # Get action from dummy AI
            action = dummy_ai.select_action(None, player, self.players, current_time)
            
            # Execute action
            if action == ACTION_SHOOT:
                bullet = player.shoot(self.players)
                if bullet:
                    self.bullets.append(bullet)
            else:
                # Apply other actions
                if action == ACTION_MOVE_UP:
                    player.move("up")
                elif action == ACTION_MOVE_DOWN:
                    player.move("down")
                elif action == ACTION_MOVE_LEFT:
                    player.move("left")
                elif action == ACTION_MOVE_RIGHT:
                    player.move("right")
                elif action == ACTION_AIM_UP:
                    player.aim("up")
                elif action == ACTION_AIM_DOWN:
                    player.aim("down")
                elif action == ACTION_AIM_LEFT:
                    player.aim("left")
                elif action == ACTION_AIM_RIGHT:
                    player.aim("right")
                elif action == ACTION_RELOAD:
                    player.reload()
    
    def _update_bullets(self, dt):
        """Update bullets and handle collisions"""
        active_bullets = []
        
        for bullet in self.bullets:
            bullet.update(dt)
            
            if not bullet.active:
                continue
            
            # Check collision with players
            hit_player = None
            for player in self.players:
                if (player.is_alive and 
                    player.player_id != bullet.owner_id and 
                    player.team != bullet.team):
                    
                    distance = np.sqrt((bullet.x - player.x)**2 + (bullet.y - player.y)**2)
                    if distance < PLAYER_RADIUS:
                        hit_player = player
                        break
            
            if hit_player:
                was_killed = hit_player.take_damage(BULLET_DAMAGE, bullet.owner_id)
                bullet.active = False
                
                if was_killed:
                    # Update scores and stats
                    killer = next((p for p in self.players if p.player_id == bullet.owner_id), None)
                    if killer:
                        killer.kills += 1
                        killer_team_num = 1 if killer.team == 'blue' else 2
                        self.team_scores[killer_team_num] += 1
                        
                        # Update match stats
                        if killer == self.ai_player:
                            self.match_stats['ai_kills'] += 1
                        
                        if hit_player == self.ai_player:
                            self.match_stats['ai_deaths'] += 1
                        
                        self.match_stats['total_kills'] += 1
                        
                        # Add to kill feed
                        killer_name = "AI" if killer == self.ai_player else f"Bot{killer.player_id}"
                        victim_name = "AI" if hit_player == self.ai_player else f"Bot{hit_player.player_id}"
                        self.kill_feed.append(f"{killer_name} → {victim_name}")
                        if len(self.kill_feed) > 8:
                            self.kill_feed.pop(0)
            
            if bullet.active:
                active_bullets.append(bullet)
        
        self.bullets = active_bullets
    
    def _update_bullet_trails(self):
        """Update visual bullet trail effects"""
        current_time = time.time()
        active_trails = []
        
        for trail in self.bullet_trails:
            if current_time - trail['time'] < trail['duration']:
                active_trails.append(trail)
        
        self.bullet_trails = active_trails

    def handle_events(self):
        """Handle pygame events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                elif event.key == pygame.K_r:
                    self._initialize_match()
                elif event.key == pygame.K_c:
                    self.crosshair_visible = not self.crosshair_visible

    def update(self, dt):
        """Update game state"""
        if self.paused:
            return

        self.game_time += dt

        # Update reward manager
        if self.reward_manager:
            self.reward_manager.update(time.time(), self.players)

        # Update players
        for player in self.players:
            player.update(dt, self.players)

        # Update AI player
        self._update_ai_player(dt)

        # Update dummy AIs
        self._update_dummy_ais(dt)

        # Update bullets
        self._update_bullets(dt)

        # Update visual effects
        self._update_bullet_trails()

        # Check match end conditions
        if self.game_time >= self.match_duration or max(self.team_scores.values()) >= 50:
            self._end_match()

    def _end_match(self):
        """End the match and show results"""
        self.match_stats['match_duration'] = self.game_time

        # Determine winner
        if self.team_scores[1] > self.team_scores[2]:
            self.match_stats['winner'] = "Team 1 (Blue)"
        elif self.team_scores[2] > self.team_scores[1]:
            self.match_stats['winner'] = "Team 2 (Red)"
        else:
            self.match_stats['winner'] = "Draw"

        # Calculate AI stats
        ai_kd_ratio = self.match_stats['ai_kills'] / max(1, self.match_stats['ai_deaths'])

        print("\n" + "="*50)
        print("🏆 MATCH RESULTS")
        print("="*50)
        print(f"Duration: {self.game_time:.1f}s")
        print(f"Winner: {self.match_stats['winner']}")
        print(f"Final Score: Team 1: {self.team_scores[1]} | Team 2: {self.team_scores[2]}")
        print(f"Total Kills: {self.match_stats['total_kills']}")
        print()
        print("🤖 AI PERFORMANCE:")
        print(f"  Kills: {self.match_stats['ai_kills']}")
        print(f"  Deaths: {self.match_stats['ai_deaths']}")
        print(f"  K/D Ratio: {ai_kd_ratio:.2f}")
        print(f"  Damage Dealt: {self.ai_player.damage_dealt}")
        print()

        # Save match log
        self._save_match_log()

        # Ask for restart
        print("Press R to restart match, ESC to exit")

    def _save_match_log(self):
        """Save match statistics to log file"""
        # Create logs directory
        os.makedirs("match_logs", exist_ok=True)

        # Prepare log data
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'match_duration': self.game_time,
            'winner': self.match_stats['winner'],
            'team_scores': self.team_scores,
            'ai_performance': {
                'kills': self.match_stats['ai_kills'],
                'deaths': self.match_stats['ai_deaths'],
                'kd_ratio': self.match_stats['ai_kills'] / max(1, self.match_stats['ai_deaths']),
                'damage_dealt': self.ai_player.damage_dealt,
                'final_hp': self.ai_player.hp,
                'final_ammo': self.ai_player.ammo
            },
            'total_kills': self.match_stats['total_kills'],
            'kill_feed': self.kill_feed.copy()
        }

        # Save to JSON file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"match_logs/tdm_match_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(log_data, f, indent=2)

        print(f"📊 Match log saved: {filename}")

    def draw(self):
        """Render the match"""
        self.screen.fill(BACKGROUND_COLOR)

        # Draw players
        for player in self.players:
            player.draw(self.screen)

        # Draw bullets
        for bullet in self.bullets:
            bullet.draw(self.screen)

        # Draw bullet trails
        for trail in self.bullet_trails:
            pygame.draw.line(self.screen, (255, 255, 100), trail['start'], trail['end'], 2)

        # Draw crosshair for AI player
        if self.crosshair_visible and self.ai_player.is_alive:
            crosshair_length = 20
            end_x = self.ai_player.x + np.cos(self.ai_player.direction) * crosshair_length
            end_y = self.ai_player.y + np.sin(self.ai_player.direction) * crosshair_length
            pygame.draw.line(self.screen, (0, 255, 255),
                           (self.ai_player.x, self.ai_player.y), (end_x, end_y), 3)

        # Draw UI
        self._draw_ui()

        pygame.display.flip()

    def _draw_ui(self):
        """Draw user interface elements"""
        # Match timer
        time_left = max(0, self.match_duration - self.game_time)
        time_text = f"Time: {int(time_left // 60):02d}:{int(time_left % 60):02d}"
        time_surface = self.font.render(time_text, True, (255, 255, 255))
        self.screen.blit(time_surface, (10, 10))

        # Team scores
        score_text = f"Blue: {self.team_scores[1]}  |  Red: {self.team_scores[2]}"
        score_surface = self.font.render(score_text, True, (255, 255, 255))
        self.screen.blit(score_surface, (10, 40))

        # AI stats
        if self.ai_player:
            ai_kd = self.match_stats['ai_kills'] / max(1, self.match_stats['ai_deaths'])
            ai_text = f"AI: HP={self.ai_player.hp} | Ammo={self.ai_player.ammo} | K/D={ai_kd:.1f}"
            ai_surface = self.font.render(ai_text, True, (0, 255, 255))
            self.screen.blit(ai_surface, (10, 70))

        # Kill feed
        for i, kill in enumerate(self.kill_feed[-8:]):  # Show last 8 kills
            kill_surface = self.small_font.render(kill, True, (255, 255, 0))
            self.screen.blit(kill_surface, (SCREEN_WIDTH - 200, 10 + i * 18))

        # Controls
        controls = [
            "SPACE: Pause | R: Restart | C: Toggle Crosshair | ESC: Exit"
        ]
        for i, control in enumerate(controls):
            control_surface = self.small_font.render(control, True, (200, 200, 200))
            self.screen.blit(control_surface, (10, SCREEN_HEIGHT - 30 + i * 15))

        # Pause indicator
        if self.paused:
            pause_text = "PAUSED - Press SPACE to resume"
            pause_surface = self.font.render(pause_text, True, (255, 255, 0))
            text_rect = pause_surface.get_rect(center=(SCREEN_WIDTH//2, SCREEN_HEIGHT//2))
            pygame.draw.rect(self.screen, (0, 0, 0), text_rect.inflate(20, 10))
            self.screen.blit(pause_surface, text_rect)

    def run(self):
        """Main game loop"""
        print("🎮 Starting TDM Match Viewer...")
        print("Controls: SPACE=Pause, R=Restart, C=Toggle Crosshair, ESC=Exit")
        print("Watch your AI compete in live 5v5 action!")

        while self.running:
            dt = self.clock.tick(30) / 1000.0  # 30 FPS

            self.handle_events()
            self.update(dt)
            self.draw()

        pygame.quit()
        print("👋 Match viewer closed")

def main():
    """Main function"""
    import sys

    model_path = "tdm_pro_ai"
    if len(sys.argv) > 1:
        model_path = sys.argv[1]

    try:
        viewer = TDMMatchViewer(model_path)
        viewer.run()
    except FileNotFoundError:
        print("❌ No trained model found!")
        print("Please train a model first:")
        print("  python rl_train.py --mode train")
        print("Or run a quick demo:")
        print("  python quick_train_demo.py train")
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
