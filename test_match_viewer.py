#!/usr/bin/env python3
"""
Test script for the TDM Match Viewer
Creates a simple trained model if none exists, then runs the viewer
"""

import os
import numpy as np
from stable_baselines3 import PPO
from rl_train import TDMEnv

def create_demo_model():
    """Create a simple demo model for testing"""
    print("🤖 Creating demo model for testing...")

    # Create environment
    env = TDMEnv(headless=True)

    # Create a simple PPO model (without training)
    model = PPO(
        "MlpPolicy",
        env,
        verbose=0,
        learning_rate=3e-4,
        n_steps=256,
        batch_size=32,
        n_epochs=3
    )

    # Save the untrained model (it will still work for demo purposes)
    model.save("tdm_demo_model")
    print("✅ Demo model saved as 'tdm_demo_model' (untrained)")

    env.close()
    return model

def test_model_loading():
    """Test loading and using a model"""
    print("🧪 Testing model loading...")
    
    # Try to load existing model
    model_path = "tdm_pro_ai"
    if not os.path.exists(f"{model_path}.zip"):
        model_path = "tdm_demo_model"
        if not os.path.exists(f"{model_path}.zip"):
            print("No model found, creating demo model...")
            create_demo_model()
            model_path = "tdm_demo_model"
    
    # Load model
    try:
        model = PPO.load(model_path)
        print(f"✅ Successfully loaded model: {model_path}")
        
        # Test prediction
        env = TDMEnv(headless=True)
        obs, info = env.reset()
        action, _ = model.predict(obs)
        print(f"✅ Model prediction test successful: action={action}")
        env.close()
        
        return model_path
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None

def run_quick_match_test():
    """Run a quick match test without the full viewer"""
    print("🎮 Running quick match test...")
    
    model_path = test_model_loading()
    if not model_path:
        return False
    
    try:
        # Import here to avoid issues if pygame not available
        from tdm_match_viewer import TDMMatchViewer
        
        print("✅ TDMMatchViewer import successful")
        
        # Test viewer creation (but don't run the full loop)
        viewer = TDMMatchViewer(model_path)
        print("✅ TDMMatchViewer creation successful")
        
        # Test one update cycle
        viewer.update(1/60)  # One frame at 60 FPS
        print("✅ Match update test successful")
        
        print("🎉 All tests passed! Match viewer is ready.")
        return True
        
    except Exception as e:
        print(f"❌ Error in match viewer test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Testing TDM Match Viewer Setup")
    print("=" * 50)
    
    # Check if we have a trained model
    has_trained_model = os.path.exists("tdm_pro_ai.zip")
    has_demo_model = os.path.exists("tdm_demo_model.zip")
    
    print(f"Trained model (tdm_pro_ai): {'✅' if has_trained_model else '❌'}")
    print(f"Demo model (tdm_demo_model): {'✅' if has_demo_model else '❌'}")
    
    if not has_trained_model and not has_demo_model:
        print("\n🤖 No models found. Creating demo model...")
        create_demo_model()
    
    # Run tests
    success = run_quick_match_test()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 READY TO WATCH YOUR AI!")
        print("=" * 50)
        print("Run the match viewer with:")
        if has_trained_model:
            print("  python tdm_match_viewer.py")
        else:
            print("  python tdm_match_viewer.py tdm_demo_model")
        print()
        print("Controls in the viewer:")
        print("  SPACE - Pause/Resume")
        print("  R - Restart match")
        print("  C - Toggle AI crosshair")
        print("  ESC - Exit")
        print()
        print("Watch for:")
        print("  🔵 Blue team (AI + 4 bots)")
        print("  🔴 Red team (5 bots)")
        print("  💥 Kill feed on the right")
        print("  📊 AI stats at the top")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
