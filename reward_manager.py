import time
from config import *

class RewardManager:
    """Track and calculate reinforcement learning rewards in the PUBG TDM sandbox"""

    def __init__(self, ai_agent_player):
        """Initialize with a reference to the AIAgent player (controlled agent)"""
        self.ai_agent = ai_agent_player  # Reference to the controlled agent
        self.ai_player_id = ai_agent_player.player_id

        # Tracking variables
        self.kills_count = 0
        self.deaths_count = 0
        self.last_kill_time = 0.0
        self.last_reward_time = time.time()  # Initialize to current time
        self.episode_start_time = time.time()
        self.total_reward = 0.0

        # State tracking for event detection
        self.last_alive_state = ai_agent_player.is_alive
        self.last_kills = ai_agent_player.kills
        self.last_deaths = ai_agent_player.deaths

        print(f"[RewardManager] Initialized for AI Player {self.ai_player_id}")
        print(f"[RewardManager] Episode started at {self.episode_start_time:.2f}")
    
    def update(self, current_time, players):
        """Called each frame to update internal reward tracking"""
        # Find the AI agent in the players list
        ai_player = None
        for player in players:
            if player.player_id == self.ai_player_id:
                ai_player = player
                break

        if ai_player is None:
            return  # AI player not found

        # Update reference (in case player object changed)
        self.ai_agent = ai_player

        # Check for kills (increase in kill count)
        if ai_player.kills > self.last_kills:
            new_kills = ai_player.kills - self.last_kills
            self.kills_count += new_kills
            self.last_kill_time = current_time
            print(f"[RewardManager] AI Player got {new_kills} kill(s)! Total: {self.kills_count}")

        # Check for deaths (increase in death count)
        if ai_player.deaths > self.last_deaths:
            new_deaths = ai_player.deaths - self.last_deaths
            self.deaths_count += new_deaths
            print(f"[RewardManager] AI Player died {new_deaths} time(s)! Total: {self.deaths_count}")

        # Check for respawn (was dead, now alive)
        if not self.last_alive_state and ai_player.is_alive:
            print(f"[RewardManager] AI Player respawned! Resetting tracking...")
            # Reset tracking on agent respawn (but keep episode totals)
            self.last_reward_time = current_time

        # Update state tracking
        self.last_alive_state = ai_player.is_alive
        self.last_kills = ai_player.kills
        self.last_deaths = ai_player.deaths
    
    def get_reward(self):
        """Return a float reward based on kills, deaths, and survival time"""
        current_time = time.time()
        reward = 0.0

        # +1.0 per kill
        kill_reward = self.kills_count * 1.0
        if kill_reward > 0:
            print(f"[RewardManager] Kill reward: +{kill_reward:.1f} ({self.kills_count} kills)")

        # -1.0 per death
        death_penalty = self.deaths_count * -1.0
        if death_penalty < 0:
            print(f"[RewardManager] Death penalty: {death_penalty:.1f} ({self.deaths_count} deaths)")

        # +0.1 per second survived (only if alive and enough time passed)
        survival_reward = 0.0
        if (self.ai_agent.is_alive and
            current_time - self.last_reward_time >= 1.0):  # Reward every second

            seconds_survived = current_time - self.last_reward_time
            survival_reward = seconds_survived * 0.1
            self.last_reward_time = current_time
            print(f"[RewardManager] Survival reward: +{survival_reward:.1f} ({seconds_survived:.1f}s)")

        # Bonus +0.5 for multi-kill (within 3 seconds)
        multi_kill_bonus = 0.0
        if (self.kills_count > 0 and
            current_time - self.last_kill_time <= 3.0 and
            self.kills_count >= 2):  # At least 2 kills for multi-kill

            # Only give bonus once per multi-kill sequence
            if not hasattr(self, '_multi_kill_awarded'):
                multi_kill_bonus = 0.5
                self._multi_kill_awarded = True
                print(f"[RewardManager] Multi-kill bonus: +{multi_kill_bonus:.1f}!")
        else:
            # Reset multi-kill tracking if too much time passed
            if current_time - self.last_kill_time > 3.0:
                self._multi_kill_awarded = False

        # Calculate total reward
        total_reward = kill_reward + death_penalty + survival_reward + multi_kill_bonus

        # Debug print for non-zero rewards
        if total_reward != 0:
            print(f"[RewardManager] Total reward this step: {total_reward:.2f}")

        # Update running total
        self.total_reward += total_reward

        return total_reward
    
    def reset(self):
        """Reset tracking for a new episode"""
        self.kills_count = 0
        self.deaths_count = 0
        self.last_kill_time = 0.0
        self.last_reward_time = time.time()
        self.episode_start_time = time.time()
        self.total_reward = 0.0

        # Reset state tracking
        self.last_alive_state = self.ai_agent.is_alive
        self.last_kills = self.ai_agent.kills
        self.last_deaths = self.ai_agent.deaths
        self._multi_kill_awarded = False

        print(f"[RewardManager] Reset for new episode")

    def get_episode_summary(self):
        """Get summary statistics for the episode"""
        current_time = time.time()
        episode_duration = current_time - self.episode_start_time

        return {
            'total_reward': self.total_reward,
            'kills': self.kills_count,
            'deaths': self.deaths_count,
            'episode_duration': episode_duration,
            'kd_ratio': self.kills_count / max(1, self.deaths_count),
            'reward_per_minute': self.total_reward / max(1, episode_duration / 60)
        }

    def get_debug_info(self):
        """Get current debug information"""
        current_time = time.time()
        return {
            'ai_player_id': self.ai_player_id,
            'ai_alive': self.ai_agent.is_alive,
            'kills': self.kills_count,
            'deaths': self.deaths_count,
            'total_reward': self.total_reward,
            'time_since_last_kill': current_time - self.last_kill_time,
            'time_since_last_reward': current_time - self.last_reward_time
        }
