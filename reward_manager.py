import math
from config import *

class RewardManager:
    """Manages reward calculation for reinforcement learning training"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all tracking variables for a new episode"""
        self.total_rewards = {}
        self.last_hp = {}
        self.last_ammo = {}
        self.last_position = {}
        self.survival_ticks = {}
        self.damage_dealt_this_episode = {}
        self.kills_this_episode = {}
        self.deaths_this_episode = {}
        self.shots_fired = {}
        self.shots_hit = {}
        self.episode_start_time = 0
    
    def initialize_player(self, player_id, player):
        """Initialize tracking for a new player"""
        self.total_rewards[player_id] = 0.0
        self.last_hp[player_id] = player.hp
        self.last_ammo[player_id] = player.ammo
        self.last_position[player_id] = (player.x, player.y)
        self.survival_ticks[player_id] = 0
        self.damage_dealt_this_episode[player_id] = 0
        self.kills_this_episode[player_id] = 0
        self.deaths_this_episode[player_id] = 0
        self.shots_fired[player_id] = 0
        self.shots_hit[player_id] = 0
    
    def calculate_step_reward(self, player_id, player, game_state):
        """Calculate reward for a single step/action"""
        if player_id not in self.total_rewards:
            self.initialize_player(player_id, player)
        
        reward = 0.0
        
        # Survival reward (small positive reward for staying alive)
        if player.alive:
            reward += REWARD_SURVIVAL_TICK
            self.survival_ticks[player_id] += 1
        
        # Health change rewards/penalties
        hp_change = player.hp - self.last_hp[player_id]
        if hp_change < 0:  # Took damage
            reward += hp_change * 0.01  # Small penalty for taking damage
        
        # Ammo management (encourage efficient shooting)
        ammo_change = self.last_ammo[player_id] - player.ammo
        if ammo_change > 0:  # Shot a bullet
            self.shots_fired[player_id] += ammo_change
        
        # Position-based rewards (encourage movement and positioning)
        current_pos = (player.x, player.y)
        last_pos = self.last_position[player_id]
        
        # Small reward for moving (prevents camping)
        distance_moved = math.sqrt((current_pos[0] - last_pos[0])**2 + 
                                 (current_pos[1] - last_pos[1])**2)
        if distance_moved > 5:  # Only reward significant movement
            reward += min(distance_moved * 0.001, 0.05)  # Cap movement reward
        
        # Proximity to enemies (encourage engagement)
        if player.alive:
            closest_enemy_distance = self._get_closest_enemy_distance(player, game_state)
            if closest_enemy_distance is not None:
                # Reward being at optimal engagement distance
                optimal_distance = BULLET_RANGE * 0.7  # 70% of max range
                distance_factor = 1.0 - abs(closest_enemy_distance - optimal_distance) / optimal_distance
                reward += max(0, distance_factor * 0.02)
        
        # Update tracking variables
        self.last_hp[player_id] = player.hp
        self.last_ammo[player_id] = player.ammo
        self.last_position[player_id] = current_pos
        
        # Add to total reward
        self.total_rewards[player_id] += reward
        
        return reward
    
    def calculate_kill_reward(self, killer_id, victim_id):
        """Calculate reward for getting a kill"""
        if killer_id not in self.total_rewards:
            return 0.0
        
        reward = REWARD_KILL
        self.kills_this_episode[killer_id] += 1
        self.total_rewards[killer_id] += reward
        
        return reward
    
    def calculate_death_penalty(self, player_id):
        """Calculate penalty for dying"""
        if player_id not in self.total_rewards:
            return 0.0
        
        penalty = REWARD_DEATH
        self.deaths_this_episode[player_id] += 1
        self.total_rewards[player_id] += penalty
        
        return penalty
    
    def calculate_hit_reward(self, shooter_id, damage_dealt):
        """Calculate reward for hitting an enemy"""
        if shooter_id not in self.total_rewards:
            return 0.0
        
        reward = REWARD_HIT * (damage_dealt / BULLET_DAMAGE)  # Scale by damage
        self.shots_hit[shooter_id] += 1
        self.damage_dealt_this_episode[shooter_id] += damage_dealt
        self.total_rewards[shooter_id] += reward
        
        return reward
    
    def calculate_miss_penalty(self, shooter_id):
        """Calculate penalty for missing a shot"""
        if shooter_id not in self.total_rewards:
            return 0.0
        
        penalty = REWARD_MISS
        self.total_rewards[shooter_id] += penalty
        
        return penalty
    
    def calculate_team_reward(self, team, game_state):
        """Calculate team-based rewards (optional)"""
        # Could implement team-based objectives here
        # For now, just individual rewards
        return 0.0
    
    def get_episode_summary(self, player_id):
        """Get summary statistics for the episode"""
        if player_id not in self.total_rewards:
            return {}
        
        accuracy = 0.0
        if self.shots_fired[player_id] > 0:
            accuracy = self.shots_hit[player_id] / self.shots_fired[player_id]
        
        return {
            'total_reward': self.total_rewards[player_id],
            'kills': self.kills_this_episode[player_id],
            'deaths': self.deaths_this_episode[player_id],
            'damage_dealt': self.damage_dealt_this_episode[player_id],
            'survival_ticks': self.survival_ticks[player_id],
            'shots_fired': self.shots_fired[player_id],
            'shots_hit': self.shots_hit[player_id],
            'accuracy': accuracy,
            'kd_ratio': self.kills_this_episode[player_id] / max(1, self.deaths_this_episode[player_id])
        }
    
    def _get_closest_enemy_distance(self, player, game_state):
        """Helper function to find distance to closest enemy"""
        if 'players' not in game_state:
            return None
        
        closest_distance = float('inf')
        found_enemy = False
        
        for other_player in game_state['players']:
            if (other_player.id != player.id and 
                other_player.team != player.team and 
                other_player.alive):
                
                distance = player.get_distance_to(other_player)
                if distance < closest_distance:
                    closest_distance = distance
                    found_enemy = True
        
        return closest_distance if found_enemy else None
    
    def get_shaped_reward(self, player_id, player, action, game_state):
        """Calculate shaped reward based on action and game state"""
        base_reward = self.calculate_step_reward(player_id, player, game_state)
        
        # Action-specific shaping
        action_reward = 0.0
        
        if action == ACTION_SHOOT:
            # Encourage shooting when enemies are nearby
            closest_enemy_dist = self._get_closest_enemy_distance(player, game_state)
            if closest_enemy_dist and closest_enemy_dist < BULLET_RANGE:
                action_reward += 0.1
        
        elif action == ACTION_RELOAD:
            # Encourage reloading when ammo is low
            if player.ammo < player.max_ammo * 0.3:  # Less than 30% ammo
                action_reward += 0.05
        
        elif action in [ACTION_MOVE_UP, ACTION_MOVE_DOWN, ACTION_MOVE_LEFT, ACTION_MOVE_RIGHT]:
            # Small reward for movement (anti-camping)
            action_reward += 0.01
        
        return base_reward + action_reward
    
    def get_total_reward(self, player_id):
        """Get total accumulated reward for a player"""
        return self.total_rewards.get(player_id, 0.0)
    
    def normalize_rewards(self, player_ids):
        """Normalize rewards across all players (optional)"""
        if not player_ids:
            return
        
        rewards = [self.total_rewards.get(pid, 0.0) for pid in player_ids]
        mean_reward = sum(rewards) / len(rewards)
        std_reward = math.sqrt(sum((r - mean_reward)**2 for r in rewards) / len(rewards))
        
        if std_reward > 0:
            for pid in player_ids:
                if pid in self.total_rewards:
                    self.total_rewards[pid] = (self.total_rewards[pid] - mean_reward) / std_reward
