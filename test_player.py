#!/usr/bin/env python3
"""
Test script for the updated Player class
Tests the new architecture with direction-based aiming and line-of-sight shooting
"""

import pygame
import math
import sys
from config import *
from player import Player

def test_player_creation():
    """Test player creation with new architecture"""
    print("Testing Player creation...")
    
    # Test with string team names
    player1 = Player(0, 'blue', 100, 100, is_ai=True)
    assert player1.player_id == 0
    assert player1.team == 'blue'
    assert player1.x == 100
    assert player1.y == 100
    assert player1.direction == 0.0
    assert player1.hp == 100
    assert player1.is_alive == True
    assert player1.ammo == 30
    assert player1.is_ai == True
    
    # Test with numeric team (backward compatibility)
    player2 = Player(1, 2, 200, 200, is_ai=False)
    assert player2.team == 'red'
    assert player2.is_ai == False
    
    print("✓ Player creation tests passed")

def test_aiming():
    """Test aiming functionality"""
    print("Testing aiming...")
    
    player = Player(0, 'blue', 100, 100)
    
    # Test aim_at method
    player.aim_at(200, 100)  # Aim right
    assert abs(player.direction - 0.0) < 0.01
    
    player.aim_at(100, 200)  # Aim down
    assert abs(player.direction - math.pi/2) < 0.01
    
    player.aim_at(0, 100)    # Aim left
    assert abs(player.direction - math.pi) < 0.01
    
    player.aim_at(100, 0)    # Aim up
    assert abs(player.direction - (-math.pi/2)) < 0.01
    
    print("✓ Aiming tests passed")

def test_movement():
    """Test movement functionality"""
    print("Testing movement...")
    
    player = Player(0, 'blue', 100, 100)
    
    # Test string-based movement
    player.move("right")
    assert player.vel_x == PLAYER_SPEED
    assert player.vel_y == 0
    
    player.move("up")
    assert player.vel_x == PLAYER_SPEED  # Should keep previous x velocity
    assert player.vel_y == -PLAYER_SPEED
    
    player.move("stop")
    assert player.vel_x == 0
    assert player.vel_y == 0
    
    # Test angle-based movement
    player.move(0)  # Move right (0 radians)
    assert abs(player.vel_x - PLAYER_SPEED) < 0.01
    assert abs(player.vel_y) < 0.01
    
    print("✓ Movement tests passed")

def test_shooting():
    """Test line-of-sight shooting"""
    print("Testing shooting...")
    
    # Create two players
    shooter = Player(0, 'blue', 100, 100)
    target = Player(1, 'red', 200, 100)  # Directly to the right
    players = [shooter, target]
    
    # Aim at target and shoot
    shooter.aim_at(target.x, target.y)
    hit_result = shooter.shoot(players)
    
    # Should hit the target
    assert hit_result == target or isinstance(hit_result, type(target))
    assert target.hp < 100  # Target should have taken damage
    assert shooter.ammo == 29  # Shooter should have used ammo
    
    print("✓ Shooting tests passed")

def test_damage_and_respawn():
    """Test damage and respawn mechanics"""
    print("Testing damage and respawn...")
    
    player = Player(0, 'blue', 100, 100)
    
    # Test taking damage
    initial_hp = player.hp
    player.take_damage(25)
    assert player.hp == initial_hp - 25
    assert player.is_alive == True
    
    # Test death
    player.take_damage(100)  # Enough to kill
    assert player.hp <= 0
    assert player.is_alive == False
    assert player.respawn_timer == RESPAWN_TIME
    
    # Test respawn
    player.respawn()
    assert player.is_alive == True
    assert player.hp == 100
    assert player.ammo == 30
    
    print("✓ Damage and respawn tests passed")

def visual_test():
    """Visual test with Pygame"""
    print("Starting visual test...")
    print("Controls:")
    print("  WASD - Move player")
    print("  Mouse - Aim")
    print("  Space - Shoot")
    print("  ESC - Exit")
    
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Player Test")
    clock = pygame.time.Clock()
    
    # Create test players
    player = Player(0, 'blue', 100, 100, is_ai=False)
    enemy = Player(1, 'red', 300, 200, is_ai=False)
    players = [player, enemy]
    
    running = True
    while running:
        dt = clock.tick(60) / 1000.0
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    hit_result = player.shoot(players)
                    if hit_result:
                        print(f"Hit! Enemy HP: {enemy.hp}")
        
        # Handle input
        keys = pygame.key.get_pressed()
        player.move("stop")
        if keys[pygame.K_w]:
            player.move("up")
        if keys[pygame.K_s]:
            player.move("down")
        if keys[pygame.K_a]:
            player.move("left")
        if keys[pygame.K_d]:
            player.move("right")
        
        # Mouse aiming
        mouse_x, mouse_y = pygame.mouse.get_pos()
        player.aim_at(mouse_x, mouse_y)
        
        # Update players
        for p in players:
            p.update(dt, players)
        
        # Draw
        screen.fill((50, 50, 50))
        for p in players:
            p.draw(screen)
        
        # Draw crosshair at mouse
        pygame.draw.line(screen, (255, 255, 255), (mouse_x-10, mouse_y), (mouse_x+10, mouse_y), 2)
        pygame.draw.line(screen, (255, 255, 255), (mouse_x, mouse_y-10), (mouse_x, mouse_y+10), 2)
        
        pygame.display.flip()
    
    pygame.quit()

def main():
    """Run all tests"""
    print("Running Player class tests...")
    print("=" * 50)
    
    try:
        test_player_creation()
        test_aiming()
        test_movement()
        test_shooting()
        test_damage_and_respawn()
        
        print("=" * 50)
        print("✓ All tests passed!")
        print()
        
        # Ask if user wants visual test
        response = input("Run visual test? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            visual_test()
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
