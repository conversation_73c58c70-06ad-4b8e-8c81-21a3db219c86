import pygame
import math
import random
import time
from config import *

class Bullet:
    def __init__(self, x, y, vel_x, vel_y, owner_id, team):
        self.x = x
        self.y = y
        self.vel_x = vel_x
        self.vel_y = vel_y
        self.owner_id = owner_id
        self.team = team
        self.distance_traveled = 0
        self.active = True
    
    def update(self, dt):
        if not self.active:
            return
        
        # Move bullet
        self.x += self.vel_x * dt
        self.y += self.vel_y * dt
        self.distance_traveled += math.sqrt(self.vel_x**2 + self.vel_y**2) * dt
        
        # Check if bullet is out of range or off screen
        if (self.distance_traveled > BULLET_RANGE or 
            self.x < 0 or self.x > SCREEN_WIDTH or 
            self.y < 0 or self.y > SCREEN_HEIGHT):
            self.active = False
    
    def draw(self, screen):
        if self.active:
            pygame.draw.circle(screen, BULLET_COLOR, (int(self.x), int(self.y)), BULLET_SIZE)

class Player:
    def __init__(self, player_id, team, x, y, is_ai=False):
        # Core attributes as specified
        self.player_id = player_id  # unique number
        self.id = player_id  # kept for backward compatibility
        self.team = team  # 'red' or 'blue' (or 1/2 for backward compatibility)
        self.x = x
        self.y = y
        self.direction = 0.0  # angle in radians (facing direction)
        self.hp = 100  # default 100
        self.is_alive = True  # bool
        self.ammo = 30  # default 30
        self.respawn_timer = 0.0  # countdown to respawn
        self.is_ai = is_ai  # True if controlled by AI

        # Additional attributes for compatibility and functionality
        self.max_hp = 100
        self.max_ammo = 30
        self.vel_x = 0
        self.vel_y = 0
        self.last_shot_time = 0.0
        self.reload_timer = 0.0
        self.is_reloading = False

        # Stats
        self.kills = 0
        self.deaths = 0
        self.damage_dealt = 0

        # Spawn position for respawning
        self.spawn_x = x
        self.spawn_y = y

        # Backward compatibility
        self.alive = self.is_alive

        # Convert team to color names if numeric
        if team == 1:
            self.team = 'blue'
        elif team == 2:
            self.team = 'red'
    
    def update(self, dt, players):
        """Update player state each frame - move, shoot, check collisions"""
        # Sync alive status
        self.alive = self.is_alive

        # If dead → decrement respawn timer → respawn if done
        if not self.is_alive:
            self.respawn_timer -= dt
            if self.respawn_timer <= 0:
                self.respawn()
            return

        # If alive: handle reloading, movement, and bounds checking
        if self.is_reloading:
            self.reload_timer -= dt
            if self.reload_timer <= 0:
                self.is_reloading = False
                self.ammo = self.max_ammo

        # Apply movement
        self.x += self.vel_x * dt
        self.y += self.vel_y * dt

        # Keep player within screen bounds
        self.x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, self.x))
        self.y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, self.y))
    
    def move(self, direction):
        """Move player in given direction - update position based on speed"""
        if not self.is_alive:
            return

        # Handle both string directions and angle-based movement
        if isinstance(direction, str):
            if direction == "up":
                self.vel_y = -PLAYER_SPEED
            elif direction == "down":
                self.vel_y = PLAYER_SPEED
            elif direction == "left":
                self.vel_x = -PLAYER_SPEED
            elif direction == "right":
                self.vel_x = PLAYER_SPEED
            elif direction == "stop_x":
                self.vel_x = 0
            elif direction == "stop_y":
                self.vel_y = 0
            elif direction == "stop":
                self.vel_x = 0
                self.vel_y = 0
        else:
            # Angle-based movement (direction in radians)
            self.vel_x = math.cos(direction) * PLAYER_SPEED
            self.vel_y = math.sin(direction) * PLAYER_SPEED
    
    def aim_at(self, target_x, target_y):
        """Aim at specific coordinates - update direction"""
        if not self.is_alive:
            return

        dx = target_x - self.x
        dy = target_y - self.y

        # Update direction (angle in radians)
        self.direction = math.atan2(dy, dx)

        # Also update aim_x, aim_y for backward compatibility
        distance = math.sqrt(dx**2 + dy**2)
        if distance > 0:
            self.aim_x = dx / distance
            self.aim_y = dy / distance
        else:
            self.aim_x = math.cos(self.direction)
            self.aim_y = math.sin(self.direction)

    def aim(self, direction):
        """Change aim direction (backward compatibility)"""
        if not self.is_alive:
            return

        if direction == "up":
            self.direction = -math.pi / 2
            self.aim_y = -1.0
            self.aim_x = 0.0
        elif direction == "down":
            self.direction = math.pi / 2
            self.aim_y = 1.0
            self.aim_x = 0.0
        elif direction == "left":
            self.direction = math.pi
            self.aim_x = -1.0
            self.aim_y = 0.0
        elif direction == "right":
            self.direction = 0.0
            self.aim_x = 1.0
            self.aim_y = 0.0

    def aim_at_point(self, target_x, target_y):
        """Backward compatibility method"""
        self.aim_at(target_x, target_y)
    
    def shoot(self, players):
        """Check line-of-sight, apply damage to first enemy hit in front"""
        if not self.is_alive or self.is_reloading or self.ammo <= 0:
            return None

        current_time = time.time()
        if current_time - self.last_shot_time < SHOOT_COOLDOWN:
            return None

        self.last_shot_time = current_time
        self.ammo -= 1

        # Calculate shooting direction
        shoot_dx = math.cos(self.direction)
        shoot_dy = math.sin(self.direction)

        # Find first enemy hit in line of sight
        hit_enemy = None
        closest_distance = BULLET_RANGE

        for player in players:
            if (player.player_id != self.player_id and
                player.team != self.team and
                player.is_alive):

                # Vector from shooter to target
                to_target_x = player.x - self.x
                to_target_y = player.y - self.y
                target_distance = math.sqrt(to_target_x**2 + to_target_y**2)

                if target_distance > BULLET_RANGE:
                    continue

                # Normalize target vector
                if target_distance > 0:
                    to_target_x /= target_distance
                    to_target_y /= target_distance

                # Check if target is in front (dot product > threshold)
                dot_product = shoot_dx * to_target_x + shoot_dy * to_target_y
                if dot_product > 0.8:  # ~36 degree cone
                    # Check if this is the closest target
                    if target_distance < closest_distance:
                        closest_distance = target_distance
                        hit_enemy = player

        # Apply damage to hit enemy
        if hit_enemy:
            was_killed = hit_enemy.take_damage(BULLET_DAMAGE, self.player_id)
            if was_killed:
                self.kills += 1
            self.damage_dealt += BULLET_DAMAGE
            return hit_enemy  # Return hit target

        # For backward compatibility, also create a bullet object
        bullet_vel_x = shoot_dx * BULLET_SPEED
        bullet_vel_y = shoot_dy * BULLET_SPEED

        spawn_offset = PLAYER_RADIUS + 5
        bullet_x = self.x + shoot_dx * spawn_offset
        bullet_y = self.y + shoot_dy * spawn_offset

        return Bullet(bullet_x, bullet_y, bullet_vel_x, bullet_vel_y, self.player_id, self.team)
    
    def reload(self):
        """Start reloading"""
        if not self.alive or self.is_reloading or self.ammo == self.max_ammo:
            return False
        
        self.is_reloading = True
        self.reload_timer = RELOAD_TIME
        return True
    
    def take_damage(self, amount, attacker_id=None):
        """Reduce HP, die if HP ≤ 0"""
        if not self.is_alive:
            return False

        self.hp -= amount

        if self.hp <= 0:
            self.die(attacker_id)
            return True

        return False

    def die(self, killer_id=None):
        """Handle player death"""
        if not self.is_alive:
            return

        self.is_alive = False
        self.alive = False  # backward compatibility
        self.hp = 0
        self.respawn_timer = RESPAWN_TIME
        self.deaths += 1

        # Reset movement
        self.vel_x = 0
        self.vel_y = 0
    
    def respawn(self):
        """Reset position, HP, ammo"""
        self.is_alive = True
        self.alive = True  # backward compatibility
        self.hp = 100  # reset to default
        self.ammo = 30  # reset to default
        self.is_reloading = False
        self.reload_timer = 0
        self.respawn_timer = 0

        # Randomize spawn position slightly
        spawn_variance = 30
        self.x = self.spawn_x + random.uniform(-spawn_variance, spawn_variance)
        self.y = self.spawn_y + random.uniform(-spawn_variance, spawn_variance)

        # Keep within bounds
        self.x = max(PLAYER_RADIUS, min(SCREEN_WIDTH - PLAYER_RADIUS, self.x))
        self.y = max(PLAYER_RADIUS, min(SCREEN_HEIGHT - PLAYER_RADIUS, self.y))
    
    def get_distance_to(self, other_player):
        """Get distance to another player"""
        dx = self.x - other_player.x
        dy = self.y - other_player.y
        return math.sqrt(dx**2 + dy**2)
    
    def draw(self, screen):
        """Draw player as colored circle with direction line and HP bar"""
        if not self.is_alive:
            return

        # Choose color based on team
        if self.team == 'blue' or self.team == 1:
            color = (0, 100, 255)  # Blue
        elif self.team == 'red' or self.team == 2:
            color = (255, 100, 0)  # Red
        else:
            color = (128, 128, 128)  # Gray for unknown team

        # Draw player as circle
        pygame.draw.circle(screen, color, (int(self.x), int(self.y)), PLAYER_RADIUS)

        # Draw direction line
        direction_length = PLAYER_RADIUS + 15
        end_x = self.x + math.cos(self.direction) * direction_length
        end_y = self.y + math.sin(self.direction) * direction_length
        pygame.draw.line(screen, (255, 255, 255), (self.x, self.y), (end_x, end_y), 2)

        # Draw HP bar above player
        bar_width = PLAYER_RADIUS * 2
        bar_height = 4
        bar_x = self.x - bar_width // 2
        bar_y = self.y - PLAYER_RADIUS - 10

        # Background (red)
        pygame.draw.rect(screen, (255, 0, 0), (bar_x, bar_y, bar_width, bar_height))
        # Health (green)
        health_width = (self.hp / 100) * bar_width
        pygame.draw.rect(screen, (0, 255, 0), (bar_x, bar_y, health_width, bar_height))

        # Draw player ID
        font = pygame.font.Font(None, 16)
        text = font.render(str(self.player_id), True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.x, self.y))
        screen.blit(text, text_rect)
