import pygame
import math
import random
import time
from config import *

class Bullet:
    def __init__(self, x, y, vel_x, vel_y, owner_id, team):
        self.x = x
        self.y = y
        self.vel_x = vel_x
        self.vel_y = vel_y
        self.owner_id = owner_id
        self.team = team
        self.distance_traveled = 0
        self.active = True
    
    def update(self, dt):
        if not self.active:
            return
        
        # Move bullet
        self.x += self.vel_x * dt
        self.y += self.vel_y * dt
        self.distance_traveled += math.sqrt(self.vel_x**2 + self.vel_y**2) * dt
        
        # Check if bullet is out of range or off screen
        if (self.distance_traveled > BULLET_RANGE or 
            self.x < 0 or self.x > SCREEN_WIDTH or 
            self.y < 0 or self.y > SCREEN_HEIGHT):
            self.active = False
    
    def draw(self, screen):
        if self.active:
            pygame.draw.circle(screen, BULLET_COLOR, (int(self.x), int(self.y)), BULLET_SIZE)

class Player:
    def __init__(self, player_id, team, x, y, is_ai=False):
        self.id = player_id
        self.team = team
        self.is_ai = is_ai
        
        # Position and movement
        self.x = x
        self.y = y
        self.vel_x = 0
        self.vel_y = 0
        
        # Combat stats
        self.hp = PLAYER_MAX_HP
        self.max_hp = PLAYER_MAX_HP
        self.ammo = PLAYER_MAX_AMMO
        self.max_ammo = PLAYER_MAX_AMMO
        
        # Aiming
        self.aim_x = 1.0  # normalized aim direction
        self.aim_y = 0.0
        
        # State
        self.alive = True
        self.respawn_timer = 0.0
        self.last_shot_time = 0.0
        self.reload_timer = 0.0
        self.is_reloading = False
        
        # Stats
        self.kills = 0
        self.deaths = 0
        self.damage_dealt = 0
        
        # Spawn position for respawning
        self.spawn_x = x
        self.spawn_y = y
    
    def update(self, dt):
        """Update player state each frame"""
        if not self.alive:
            self.respawn_timer -= dt
            if self.respawn_timer <= 0:
                self.respawn()
            return
        
        # Update reload timer
        if self.is_reloading:
            self.reload_timer -= dt
            if self.reload_timer <= 0:
                self.is_reloading = False
                self.ammo = self.max_ammo
        
        # Apply movement
        self.x += self.vel_x * dt
        self.y += self.vel_y * dt
        
        # Keep player within screen bounds
        self.x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, self.x))
        self.y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, self.y))
    
    def move(self, direction, dt):
        """Move player in given direction"""
        if not self.alive:
            return
        
        if direction == "up":
            self.vel_y = -PLAYER_SPEED
        elif direction == "down":
            self.vel_y = PLAYER_SPEED
        elif direction == "left":
            self.vel_x = -PLAYER_SPEED
        elif direction == "right":
            self.vel_x = PLAYER_SPEED
        elif direction == "stop_x":
            self.vel_x = 0
        elif direction == "stop_y":
            self.vel_y = 0
        elif direction == "stop":
            self.vel_x = 0
            self.vel_y = 0
    
    def aim(self, direction):
        """Change aim direction"""
        if not self.alive:
            return
        
        if direction == "up":
            self.aim_y = -1.0
            self.aim_x = 0.0
        elif direction == "down":
            self.aim_y = 1.0
            self.aim_x = 0.0
        elif direction == "left":
            self.aim_x = -1.0
            self.aim_y = 0.0
        elif direction == "right":
            self.aim_x = 1.0
            self.aim_y = 0.0
    
    def aim_at_point(self, target_x, target_y):
        """Aim at specific coordinates"""
        if not self.alive:
            return
        
        dx = target_x - self.x
        dy = target_y - self.y
        distance = math.sqrt(dx**2 + dy**2)
        
        if distance > 0:
            self.aim_x = dx / distance
            self.aim_y = dy / distance
    
    def shoot(self):
        """Attempt to shoot a bullet"""
        if not self.alive or self.is_reloading or self.ammo <= 0:
            return None
        
        current_time = time.time()
        if current_time - self.last_shot_time < SHOOT_COOLDOWN:
            return None
        
        self.last_shot_time = current_time
        self.ammo -= 1
        
        # Create bullet
        bullet_vel_x = self.aim_x * BULLET_SPEED
        bullet_vel_y = self.aim_y * BULLET_SPEED
        
        # Spawn bullet slightly in front of player
        spawn_offset = PLAYER_SIZE + 5
        bullet_x = self.x + self.aim_x * spawn_offset
        bullet_y = self.y + self.aim_y * spawn_offset
        
        return Bullet(bullet_x, bullet_y, bullet_vel_x, bullet_vel_y, self.id, self.team)
    
    def reload(self):
        """Start reloading"""
        if not self.alive or self.is_reloading or self.ammo == self.max_ammo:
            return False
        
        self.is_reloading = True
        self.reload_timer = RELOAD_TIME
        return True
    
    def take_damage(self, damage, attacker_id=None):
        """Take damage and handle death"""
        if not self.alive:
            return False
        
        self.hp -= damage
        
        if self.hp <= 0:
            self.die(attacker_id)
            return True
        
        return False
    
    def die(self, killer_id=None):
        """Handle player death"""
        if not self.alive:
            return
        
        self.alive = False
        self.hp = 0
        self.respawn_timer = RESPAWN_TIME
        self.deaths += 1
        
        # Reset movement
        self.vel_x = 0
        self.vel_y = 0
    
    def respawn(self):
        """Respawn player at spawn location"""
        self.alive = True
        self.hp = self.max_hp
        self.ammo = self.max_ammo
        self.is_reloading = False
        self.reload_timer = 0
        
        # Randomize spawn position slightly
        spawn_variance = 30
        self.x = self.spawn_x + random.uniform(-spawn_variance, spawn_variance)
        self.y = self.spawn_y + random.uniform(-spawn_variance, spawn_variance)
        
        # Keep within bounds
        self.x = max(PLAYER_SIZE, min(SCREEN_WIDTH - PLAYER_SIZE, self.x))
        self.y = max(PLAYER_SIZE, min(SCREEN_HEIGHT - PLAYER_SIZE, self.y))
    
    def get_distance_to(self, other_player):
        """Get distance to another player"""
        dx = self.x - other_player.x
        dy = self.y - other_player.y
        return math.sqrt(dx**2 + dy**2)
    
    def draw(self, screen):
        """Draw player on screen"""
        if not self.alive:
            return
        
        # Choose color based on team
        color = TEAM_1_COLOR if self.team == 1 else TEAM_2_COLOR
        
        # Draw player as circle
        pygame.draw.circle(screen, color, (int(self.x), int(self.y)), PLAYER_SIZE)
        
        # Draw health bar if enabled
        if SHOW_PLAYER_HEALTH:
            bar_width = PLAYER_SIZE * 2
            bar_height = 4
            bar_x = self.x - bar_width // 2
            bar_y = self.y - PLAYER_SIZE - 10
            
            # Background
            pygame.draw.rect(screen, (255, 0, 0), (bar_x, bar_y, bar_width, bar_height))
            # Health
            health_width = (self.hp / self.max_hp) * bar_width
            pygame.draw.rect(screen, (0, 255, 0), (bar_x, bar_y, health_width, bar_height))
        
        # Draw aim direction if enabled
        if SHOW_AIM_DIRECTION:
            aim_length = PLAYER_SIZE + 15
            end_x = self.x + self.aim_x * aim_length
            end_y = self.y + self.aim_y * aim_length
            pygame.draw.line(screen, (255, 255, 255), (self.x, self.y), (end_x, end_y), 2)
        
        # Draw player ID
        font = pygame.font.Font(None, 16)
        text = font.render(str(self.id), True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.x, self.y))
        screen.blit(text, text_rect)
