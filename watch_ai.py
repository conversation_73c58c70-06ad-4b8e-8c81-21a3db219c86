#!/usr/bin/env python3
"""
Simple launcher for watching your AI in action
Automatically detects available models and launches the match viewer
"""

import os
import sys

def main():
    """Launch the TDM Match Viewer with the best available model"""
    print("🎮 AI Match Viewer Launcher")
    print("=" * 40)
    
    # Check for available models
    models = []
    
    if os.path.exists("tdm_pro_ai.zip"):
        models.append(("tdm_pro_ai", "Trained AI Model"))
    
    if os.path.exists("tdm_demo_model.zip"):
        models.append(("tdm_demo_model", "Demo Model"))
    
    if not models:
        print("❌ No AI models found!")
        print()
        print("To create a model:")
        print("  1. Quick demo: python test_match_viewer.py")
        print("  2. Train AI:   python rl_train.py --mode train")
        print("  3. Quick train: python quick_train_demo.py train")
        return
    
    # Show available models
    print("Available AI models:")
    for i, (model_path, description) in enumerate(models):
        print(f"  {i+1}. {description} ({model_path})")
    
    # Auto-select best model or let user choose
    if len(models) == 1:
        selected_model = models[0][0]
        print(f"\n🤖 Using: {models[0][1]}")
    else:
        print(f"\n🤖 Auto-selecting: {models[0][1]} (best available)")
        selected_model = models[0][0]
    
    print(f"🎬 Launching match viewer...")
    print()
    print("Controls:")
    print("  SPACE - Pause/Resume")
    print("  R - Restart match") 
    print("  C - Toggle AI crosshair")
    print("  ESC - Exit")
    print()
    print("Watch your AI compete in live 5v5 TDM action! 🔥")
    print("=" * 40)
    
    # Launch the viewer
    try:
        from tdm_match_viewer import TDMMatchViewer
        viewer = TDMMatchViewer(selected_model)
        viewer.run()
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Try running: python test_match_viewer.py")

if __name__ == "__main__":
    main()
