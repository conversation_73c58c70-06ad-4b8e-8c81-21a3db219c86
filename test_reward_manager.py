#!/usr/bin/env python3
"""
Test script for the updated RewardManager class
Tests the new architecture with AI agent tracking and reward calculation
"""

import time
import sys
from config import *
from player import Player
from reward_manager import RewardManager

def test_reward_manager_creation():
    """Test RewardManager creation with AI agent"""
    print("Testing RewardManager creation...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    
    # Create reward manager
    reward_manager = <PERSON>wardManager(ai_player)
    
    assert reward_manager.ai_player_id == 0
    assert reward_manager.kills_count == 0
    assert reward_manager.deaths_count == 0
    assert reward_manager.total_reward == 0.0
    
    print("✓ RewardManager creation test passed")

def test_kill_tracking():
    """Test kill tracking and rewards"""
    print("Testing kill tracking...")
    
    # Create AI player and enemy
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    enemy = Player(1, 'red', 200, 100, is_ai=False)
    players = [ai_player, enemy]
    
    # Create reward manager
    reward_manager = <PERSON>wardManager(ai_player)
    
    # Simulate AI player getting a kill
    ai_player.kills = 1  # Simulate kill
    
    # Update reward manager
    current_time = time.time()
    reward_manager.update(current_time, players)
    
    # Get reward
    reward = reward_manager.get_reward()
    
    assert reward_manager.kills_count == 1
    assert reward >= 1.0  # Should get +1.0 for kill
    
    print("✓ Kill tracking test passed")

def test_death_tracking():
    """Test death tracking and penalties"""
    print("Testing death tracking...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    # Simulate AI player death
    ai_player.deaths = 1  # Simulate death
    ai_player.is_alive = False
    
    # Update reward manager
    current_time = time.time()
    reward_manager.update(current_time, players)
    
    # Get reward
    reward = reward_manager.get_reward()
    
    assert reward_manager.deaths_count == 1
    assert reward <= -1.0  # Should get -1.0 for death
    
    print("✓ Death tracking test passed")

def test_survival_rewards():
    """Test survival time rewards"""
    print("Testing survival rewards...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    # Wait a bit and update
    time.sleep(1.1)  # Wait more than 1 second
    current_time = time.time()
    reward_manager.update(current_time, players)
    
    # Get reward
    reward = reward_manager.get_reward()
    
    # Should get survival reward (approximately +0.1 per second)
    assert reward > 0.05  # Should be around 0.1
    
    print("✓ Survival reward test passed")

def test_multi_kill_bonus():
    """Test multi-kill bonus"""
    print("Testing multi-kill bonus...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    current_time = time.time()
    
    # Simulate first kill
    ai_player.kills = 1
    reward_manager.update(current_time, players)
    reward1 = reward_manager.get_reward()
    
    # Simulate second kill within 3 seconds
    ai_player.kills = 2
    reward_manager.update(current_time + 1.0, players)  # 1 second later
    reward2 = reward_manager.get_reward()
    
    # Should get multi-kill bonus
    total_reward = reward_manager.total_reward
    assert total_reward >= 2.5  # 2 kills (2.0) + multi-kill bonus (0.5)
    
    print("✓ Multi-kill bonus test passed")

def test_respawn_reset():
    """Test respawn tracking reset"""
    print("Testing respawn reset...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    current_time = time.time()
    
    # Simulate death
    ai_player.is_alive = False
    reward_manager.update(current_time, players)
    
    # Simulate respawn
    ai_player.is_alive = True
    reward_manager.update(current_time + 1.0, players)
    
    # Should detect respawn
    assert reward_manager.last_alive_state == True
    
    print("✓ Respawn reset test passed")

def test_episode_summary():
    """Test episode summary"""
    print("Testing episode summary...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    # Simulate some activity
    ai_player.kills = 2
    ai_player.deaths = 1
    reward_manager.update(time.time(), players)
    reward_manager.get_reward()
    
    # Get summary
    summary = reward_manager.get_episode_summary()
    
    assert 'total_reward' in summary
    assert 'kills' in summary
    assert 'deaths' in summary
    assert 'kd_ratio' in summary
    assert summary['kills'] == 2
    assert summary['deaths'] == 1
    
    print("✓ Episode summary test passed")

def test_debug_info():
    """Test debug information"""
    print("Testing debug info...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    # Get debug info
    debug_info = reward_manager.get_debug_info()
    
    assert 'ai_player_id' in debug_info
    assert 'ai_alive' in debug_info
    assert 'total_reward' in debug_info
    assert debug_info['ai_player_id'] == 0
    
    print("✓ Debug info test passed")

def integration_test():
    """Integration test with realistic scenario"""
    print("Running integration test...")
    
    # Create AI player and enemies
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    enemy1 = Player(1, 'red', 200, 100, is_ai=False)
    enemy2 = Player(2, 'red', 300, 100, is_ai=False)
    players = [ai_player, enemy1, enemy2]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    current_time = time.time()
    
    print("  Scenario: AI survives 2 seconds, gets 2 kills, then dies")
    
    # Survive for 2 seconds
    time.sleep(1.1)
    reward_manager.update(time.time(), players)
    reward1 = reward_manager.get_reward()
    print(f"  After 1s survival: reward = {reward1:.2f}")
    
    time.sleep(1.1)
    reward_manager.update(time.time(), players)
    reward2 = reward_manager.get_reward()
    print(f"  After 2s survival: reward = {reward2:.2f}")
    
    # Get first kill
    ai_player.kills = 1
    reward_manager.update(time.time(), players)
    reward3 = reward_manager.get_reward()
    print(f"  After 1st kill: reward = {reward3:.2f}")
    
    # Get second kill (multi-kill)
    ai_player.kills = 2
    reward_manager.update(time.time(), players)
    reward4 = reward_manager.get_reward()
    print(f"  After 2nd kill (multi-kill): reward = {reward4:.2f}")
    
    # Die
    ai_player.deaths = 1
    ai_player.is_alive = False
    reward_manager.update(time.time(), players)
    reward5 = reward_manager.get_reward()
    print(f"  After death: reward = {reward5:.2f}")
    
    # Final summary
    summary = reward_manager.get_episode_summary()
    print(f"  Final summary: {summary}")
    
    assert summary['kills'] == 2
    assert summary['deaths'] == 1
    assert summary['total_reward'] > 0  # Should be positive overall
    
    print("✓ Integration test passed")

def main():
    """Run all tests"""
    print("Running RewardManager tests...")
    print("=" * 50)
    
    try:
        test_reward_manager_creation()
        test_kill_tracking()
        test_death_tracking()
        test_survival_rewards()
        test_multi_kill_bonus()
        test_respawn_reset()
        test_episode_summary()
        test_debug_info()
        
        print("=" * 50)
        print("✓ All unit tests passed!")
        print()
        
        integration_test()
        
        print("=" * 50)
        print("✓ All tests passed successfully!")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
