#!/usr/bin/env python3
"""
Quick training demonstration for the TDM environment
Shows how to use Stable-Baselines3 PPO with the custom TDMEnv
"""

from stable_baselines3 import PPO
from stable_baselines3.common.callbacks import BaseCallback
from rl_train import TDMEnv
import numpy as np

class QuickTrainingCallback(BaseCallback):
    """Callback for monitoring training progress"""
    
    def __init__(self, verbose=0):
        super().__init__(verbose)
        self.episode_rewards = []
        self.episode_lengths = []
        self.best_reward = -float('inf')
    
    def _on_step(self) -> bool:
        # Check if episode ended
        if self.locals.get('dones', [False])[0]:
            info = self.locals.get('infos', [{}])[0]
            
            if 'total_reward' in info:
                episode_reward = info['total_reward']
                self.episode_rewards.append(episode_reward)
                
                if episode_reward > self.best_reward:
                    self.best_reward = episode_reward
                
                # Print progress every 10 episodes
                if len(self.episode_rewards) % 10 == 0:
                    recent_rewards = self.episode_rewards[-10:]
                    avg_reward = np.mean(recent_rewards)
                    print(f"Episode {len(self.episode_rewards)}: "
                          f"Avg Reward (last 10): {avg_reward:.2f}, "
                          f"Best: {self.best_reward:.2f}, "
                          f"K/D: {info.get('ai_player_kills', 0)}/{info.get('ai_player_deaths', 0)}")
        
        return True

def quick_train_demo():
    """Quick training demonstration"""
    print("🎮 TDM AI Training Demo")
    print("=" * 50)
    
    # Create environment
    print("Creating TDM environment...")
    env = TDMEnv(headless=True)
    
    print(f"Action space: {env.action_space}")
    print(f"Observation space: {env.observation_space}")
    
    # Create PPO model
    print("\nCreating PPO model...")
    model = PPO(
        "MlpPolicy",
        env,
        verbose=1,
        learning_rate=3e-4,
        n_steps=512,  # Smaller for quick demo
        batch_size=32,
        n_epochs=5,
        gamma=0.99,
        gae_lambda=0.95,
        clip_range=0.2,
        ent_coef=0.01,
        tensorboard_log="./tdm_demo_logs/"
    )
    
    # Create callback
    callback = QuickTrainingCallback()
    
    # Train for a short time
    print("\nStarting training (5000 timesteps)...")
    print("This will take a few minutes...")
    
    model.learn(
        total_timesteps=5000,
        callback=callback,
        progress_bar=True
    )
    
    # Save model
    model.save("tdm_demo_model")
    print("\nModel saved as 'tdm_demo_model'")
    
    # Test the trained model
    print("\nTesting trained model...")
    test_trained_model(env, model, episodes=3)
    
    env.close()
    print("\n🎉 Demo completed!")

def test_trained_model(env, model, episodes=3):
    """Test the trained model"""
    for episode in range(episodes):
        obs, info = env.reset()
        total_reward = 0
        steps = 0
        
        print(f"\nEpisode {episode + 1}:")
        
        while True:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, terminated, truncated, info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            if terminated or truncated or steps >= 500:  # Limit steps for demo
                break
        
        print(f"  Total Reward: {total_reward:.2f}")
        print(f"  Steps: {steps}")
        print(f"  K/D: {info['ai_player_kills']}/{info['ai_player_deaths']}")
        print(f"  Final HP: {info['ai_player_hp']}")

def compare_random_vs_trained():
    """Compare random agent vs trained agent"""
    print("\n🔄 Comparing Random vs Trained Agent")
    print("=" * 50)
    
    env = TDMEnv(headless=True)
    
    # Test random agent
    print("Testing random agent...")
    random_rewards = []
    for episode in range(5):
        obs, info = env.reset()
        total_reward = 0
        steps = 0
        
        while True:
            action = env.action_space.sample()  # Random action
            obs, reward, terminated, truncated, info = env.step(action)
            
            total_reward += reward
            steps += 1
            
            if terminated or truncated or steps >= 300:
                break
        
        random_rewards.append(total_reward)
    
    avg_random = np.mean(random_rewards)
    print(f"Random agent average reward: {avg_random:.2f}")
    
    # Test trained agent (if exists)
    try:
        model = PPO.load("tdm_demo_model")
        print("Testing trained agent...")
        
        trained_rewards = []
        for episode in range(5):
            obs, info = env.reset()
            total_reward = 0
            steps = 0
            
            while True:
                action, _ = model.predict(obs, deterministic=True)
                obs, reward, terminated, truncated, info = env.step(action)
                
                total_reward += reward
                steps += 1
                
                if terminated or truncated or steps >= 300:
                    break
            
            trained_rewards.append(total_reward)
        
        avg_trained = np.mean(trained_rewards)
        print(f"Trained agent average reward: {avg_trained:.2f}")
        print(f"Improvement: {avg_trained - avg_random:.2f} ({((avg_trained - avg_random) / abs(avg_random) * 100):.1f}%)")
        
    except FileNotFoundError:
        print("No trained model found. Run training first!")
    
    env.close()

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "train":
            quick_train_demo()
        elif sys.argv[1] == "compare":
            compare_random_vs_trained()
        else:
            print("Usage: python quick_train_demo.py [train|compare]")
    else:
        print("TDM AI Training Demo")
        print("Options:")
        print("  python quick_train_demo.py train    - Run training demo")
        print("  python quick_train_demo.py compare  - Compare random vs trained")
        
        choice = input("\nChoose option (train/compare): ").lower().strip()
        if choice == "train":
            quick_train_demo()
        elif choice == "compare":
            compare_random_vs_trained()
        else:
            print("Invalid choice!")

if __name__ == "__main__":
    main()
