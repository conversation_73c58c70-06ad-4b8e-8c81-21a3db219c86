#!/usr/bin/env python3
"""
Example usage of the RewardManager class
Demonstrates how to integrate reward tracking into the game loop
"""

import time
import pygame
from config import *
from player import Player
from reward_manager import RewardManager

def example_game_loop():
    """Example game loop showing RewardManager integration"""
    print("Starting example game with RewardManager...")
    
    # Initialize Pygame
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("RewardManager Example")
    clock = pygame.time.Clock()
    
    # Create players
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    enemy1 = Player(1, 'red', 300, 200, is_ai=False)
    enemy2 = Player(2, 'red', 500, 300, is_ai=False)
    players = [ai_player, enemy1, enemy2]
    
    # Create reward manager for AI player
    reward_manager = RewardManager(ai_player)
    
    # Game state
    running = True
    total_reward = 0.0
    last_update_time = time.time()
    
    print("Controls:")
    print("  WASD - Move AI player")
    print("  Mouse - Aim AI player")
    print("  Space - Shoot")
    print("  K - Simulate kill")
    print("  D - Simulate death")
    print("  ESC - Exit")
    print()
    
    while running:
        dt = clock.tick(60) / 1000.0
        current_time = time.time()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_k:
                    # Simulate kill
                    ai_player.kills += 1
                    print(f"Simulated kill! AI kills: {ai_player.kills}")
                elif event.key == pygame.K_d:
                    # Simulate death
                    ai_player.deaths += 1
                    ai_player.is_alive = False
                    print(f"Simulated death! AI deaths: {ai_player.deaths}")
                elif event.key == pygame.K_r:
                    # Respawn
                    ai_player.respawn()
                    print("AI respawned!")
                elif event.key == pygame.K_SPACE:
                    # Shoot
                    hit_result = ai_player.shoot(players)
                    if hit_result and hasattr(hit_result, 'hp'):
                        print(f"AI shot hit! Target HP: {hit_result.hp}")
        
        # Handle input for AI player
        keys = pygame.key.get_pressed()
        ai_player.move("stop")
        if keys[pygame.K_w]:
            ai_player.move("up")
        if keys[pygame.K_s]:
            ai_player.move("down")
        if keys[pygame.K_a]:
            ai_player.move("left")
        if keys[pygame.K_d]:
            ai_player.move("right")
        
        # Mouse aiming
        mouse_x, mouse_y = pygame.mouse.get_pos()
        ai_player.aim_at(mouse_x, mouse_y)
        
        # Update players
        for player in players:
            player.update(dt, players)
        
        # Update reward manager
        reward_manager.update(current_time, players)
        
        # Get reward every frame
        step_reward = reward_manager.get_reward()
        total_reward += step_reward
        
        # Print reward info every 2 seconds
        if current_time - last_update_time >= 2.0:
            debug_info = reward_manager.get_debug_info()
            print(f"Reward update: Step={step_reward:.2f}, Total={total_reward:.2f}, "
                  f"K/D={debug_info['kills']}/{debug_info['deaths']}")
            last_update_time = current_time
        
        # Draw everything
        screen.fill((50, 50, 50))
        
        # Draw players
        for player in players:
            player.draw(screen)
        
        # Draw crosshair
        pygame.draw.line(screen, (255, 255, 255), (mouse_x-10, mouse_y), (mouse_x+10, mouse_y), 2)
        pygame.draw.line(screen, (255, 255, 255), (mouse_x, mouse_y-10), (mouse_x, mouse_y+10), 2)
        
        # Draw UI
        font = pygame.font.Font(None, 24)
        
        # Reward info
        reward_text = f"Total Reward: {total_reward:.2f}"
        reward_surface = font.render(reward_text, True, (255, 255, 0))
        screen.blit(reward_surface, (10, 10))
        
        # AI stats
        stats_text = f"AI: HP={ai_player.hp} Ammo={ai_player.ammo} K/D={ai_player.kills}/{ai_player.deaths}"
        stats_surface = font.render(stats_text, True, (0, 255, 255))
        screen.blit(stats_surface, (10, 40))
        
        # Instructions
        inst_font = pygame.font.Font(None, 16)
        instructions = [
            "WASD: Move | Mouse: Aim | Space: Shoot",
            "K: Simulate Kill | D: Simulate Death | R: Respawn"
        ]
        for i, inst in enumerate(instructions):
            inst_surface = inst_font.render(inst, True, (200, 200, 200))
            screen.blit(inst_surface, (10, SCREEN_HEIGHT - 40 + i * 20))
        
        pygame.display.flip()
    
    # Final summary
    print("\nFinal Episode Summary:")
    summary = reward_manager.get_episode_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    pygame.quit()

def simple_simulation():
    """Simple simulation without graphics"""
    print("Running simple reward simulation...")
    
    # Create AI player
    ai_player = Player(0, 'blue', 100, 100, is_ai=True)
    players = [ai_player]
    
    # Create reward manager
    reward_manager = RewardManager(ai_player)
    
    current_time = time.time()
    
    print("Simulating 5 seconds of survival...")
    for i in range(5):
        time.sleep(1)
        reward_manager.update(time.time(), players)
        reward = reward_manager.get_reward()
        print(f"  Second {i+1}: Reward = {reward:.2f}")
    
    print("\nSimulating 2 kills...")
    ai_player.kills = 1
    reward_manager.update(time.time(), players)
    reward1 = reward_manager.get_reward()
    print(f"  Kill 1: Reward = {reward1:.2f}")
    
    ai_player.kills = 2
    reward_manager.update(time.time(), players)
    reward2 = reward_manager.get_reward()
    print(f"  Kill 2 (multi-kill): Reward = {reward2:.2f}")
    
    print("\nSimulating death...")
    ai_player.deaths = 1
    ai_player.is_alive = False
    reward_manager.update(time.time(), players)
    death_reward = reward_manager.get_reward()
    print(f"  Death: Reward = {death_reward:.2f}")
    
    print("\nFinal Summary:")
    summary = reward_manager.get_episode_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "sim":
        simple_simulation()
    else:
        example_game_loop()

if __name__ == "__main__":
    main()
