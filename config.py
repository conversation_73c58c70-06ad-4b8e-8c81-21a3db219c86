# Game Configuration Constants
import pygame

# Screen dimensions
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600
FPS = 60

# Game settings
PLAYERS_PER_TEAM = 5
TOTAL_PLAYERS = PLAYERS_PER_TEAM * 2
GAME_DURATION = 300  # 5 minutes in seconds
RESPAWN_TIME = 3.0  # 3 seconds

# Player settings
PLAYER_SIZE = 20
PLAYER_SPEED = 100  # pixels per second
PLAYER_MAX_HP = 100
PLAYER_MAX_AMMO = 30
RELOAD_TIME = 2.0  # seconds
SHOOT_COOLDOWN = 0.1  # seconds between shots

# Weapon settings
BULLET_SPEED = 400  # pixels per second
BULLET_RANGE = 200  # pixels
BULLET_DAMAGE = 25
BULLET_SIZE = 3

# Map settings
MAP_BORDER = 50  # pixels from edge
SPAWN_SAFE_DISTANCE = 100  # minimum distance between spawns

# Team colors
TEAM_1_COLOR = (0, 100, 255)  # Blue
TEAM_2_COLOR = (255, 100, 0)  # Red
NEUTRAL_COLOR = (128, 128, 128)  # Gray
BACKGROUND_COLOR = (50, 50, 50)  # Dark gray
BULLET_COLOR = (255, 255, 0)  # Yellow

# AI/RL settings
REWARD_KILL = 1.0
REWARD_DEATH = -1.0
REWARD_SURVIVAL_TICK = 0.1
REWARD_HIT = 0.2
REWARD_MISS = -0.05

# Action space for AI
ACTION_MOVE_UP = 0
ACTION_MOVE_DOWN = 1
ACTION_MOVE_LEFT = 2
ACTION_MOVE_RIGHT = 3
ACTION_AIM_UP = 4
ACTION_AIM_DOWN = 5
ACTION_AIM_LEFT = 6
ACTION_AIM_RIGHT = 7
ACTION_SHOOT = 8
ACTION_RELOAD = 9
ACTION_NO_OP = 10

NUM_ACTIONS = 11

# Observation space dimensions
OBS_PLAYER_FEATURES = 8  # x, y, hp, ammo, aim_x, aim_y, team, alive
OBS_ENEMY_FEATURES = 6   # x, y, hp, team, alive, distance
OBS_MAX_ENEMIES = 9      # maximum other players to observe
OBS_BULLET_FEATURES = 4  # x, y, vel_x, vel_y
OBS_MAX_BULLETS = 20     # maximum bullets to observe

# Calculate total observation space size
OBS_SIZE = (OBS_PLAYER_FEATURES + 
           OBS_MAX_ENEMIES * OBS_ENEMY_FEATURES + 
           OBS_MAX_BULLETS * OBS_BULLET_FEATURES)

# Spawn positions for teams (will be randomized around these points)
TEAM_1_SPAWN_AREAS = [
    (100, 150), (100, 300), (100, 450),
    (200, 100), (200, 500)
]

TEAM_2_SPAWN_AREAS = [
    (700, 150), (700, 300), (700, 450),
    (600, 100), (600, 500)
]

# Debug settings
DEBUG_MODE = False
SHOW_BULLET_TRAILS = True
SHOW_PLAYER_HEALTH = True
SHOW_AIM_DIRECTION = True
